{{ define "probe.title" }}
{{- if eq .Status "firing" -}}
❗ [{{ .Status | toUpper }}:{{ len .Alerts }}] URI 健康检查出现异常 ! ! !
{{- else if eq .Status "resolved" -}}
✅ [{{ .Status | toUpper }}:{{ len .Alerts }}] URI 健康检查恢复正常 ~ ~ ~
{{- end -}}
{{- end -}}

{{ define "probe.print_alert" -}}
{{ if .Annotations -}}
Summary:
{{ .Annotations.summary }}
{{ end -}}
{{ if .DashboardURL -}}
Go to Dashboard: https://grafana.yakumaru.ai/d/xtkCtBkiz/neox-domains-and-certs-monitor
{{- end }}
{{- end -}}

{{ define "probe.message" -}}
{{ if .Alerts.Firing -}}
{{ range .Alerts.Firing }}
{{ template "probe.print_alert" . }}
{{ end -}}
{{ end -}}
{{ if .Alerts.Resolved -}}
{{ range .Alerts.Resolved }}
{{ template "probe.print_alert" .}}
{{ end -}}
{{ end -}}
{{- end -}}