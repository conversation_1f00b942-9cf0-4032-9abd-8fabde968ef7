{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Redis Dashboard", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 11, "links": [{"icon": "external link", "tags": [], "targetBlank": true, "title": "RedisGrafana", "type": "link", "url": "https://redisgrafana.github.io/"}, {"asDropdown": false, "icon": "dashboard", "includeVars": true, "keepTime": false, "tags": [], "targetBlank": true, "title": "Redis Streaming", "tooltip": "Open Redis Streaming Dashboard", "type": "link", "url": "/d/E_yGWieGz/redis-streaming"}], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 56, "panels": [], "repeat": "redis", "repeatDirection": "h", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Summary", "type": "row"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-green", "value": null}, {"color": "dark-yellow", "value": 22000}, {"color": "dark-red", "value": 25000}]}, "unit": "ops", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 1}, "id": 24, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"command": "info", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "section": "stats", "type": "command"}], "title": "Ops/sec", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["instantaneous_ops_per_sec"]}}}], "type": "stat"}, {"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [], "max": 11000, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "dark-green", "value": null}, {"color": "dark-yellow", "value": 8000}, {"color": "dark-red", "value": 10000}]}, "unit": "KBs", "unitScale": true}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 3, "y": 1}, "id": 25, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto", "text": {}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "panelId": 24, "refId": "A"}], "title": "Network", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["instantaneous_input_kbps", "instantaneous_output_kbps"]}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"instantaneous_input_kbps": "Input", "instantaneous_output_kbps": "Output"}}}], "type": "gauge"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "dark-green", "value": null}]}, "unit": "decbytes", "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Used Memory"}, "properties": [{"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Used Memory, Peak"}, "properties": [{"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Used Memory, LUA"}, "properties": [{"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Limit"}, "properties": [{"id": "decimals", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total System Memory"}, "properties": [{"id": "decimals", "value": 2}]}]}, "gridPos": {"h": 6, "w": 10, "x": 11, "y": 1}, "id": 8, "options": {"displayMode": "lcd", "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "10.3.3", "targets": [{"command": "info", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "section": "memory", "type": "command"}], "title": "Memory", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["used_memory", "used_memory_peak", "total_system_memory", "maxmemory", "used_memory_lua"]}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"maxmemory": 3, "total_system_memory": 4, "used_memory": 0, "used_memory_lua": 2, "used_memory_peak": 1}, "renameByName": {"maxmemory": "Memory Limit", "total_system_memory": "Total System Memory", "used_memory": "Used Memory", "used_memory_lua": "Used Memory, LUA", "used_memory_peak": "Used Memory, Peak"}}}], "type": "bargauge"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-blue", "value": null}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 1}, "id": 19, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"command": "info", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "section": "server", "type": "command"}], "title": "Uptime", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["uptime_in_seconds"]}}}], "type": "stat"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-blue", "value": null}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 4}, "id": 22, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"command": "info", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "section": "clients", "type": "command"}], "title": "Connected Clients", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["connected_clients"]}}}], "type": "stat"}, {"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-blue", "value": null}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 4}, "id": 6, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "panelId": 19, "refId": "A"}], "title": "Version", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["redis_version"]}}}], "type": "stat"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-blue", "value": null}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 0, "y": 7}, "id": 2, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"uid": "$redis"}, "query": "dbsize", "refId": "A", "type": "cli"}], "title": "Number of Keys", "type": "stat"}, {"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "fieldConfig": {"defaults": {"decimals": 0, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "dark-green", "value": null}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 8, "x": 3, "y": 7}, "id": 36, "options": {"displayMode": "lcd", "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "panelId": 24, "refId": "A"}], "title": "Keys", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["expired_keys", "evicted_keys"]}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"evicted_keys": "Evicted", "expired_keys": "Expired"}}}], "type": "bargauge"}, {"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "fieldConfig": {"defaults": {"decimals": 0, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "dark-green", "value": null}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 10, "x": 11, "y": 7}, "id": 38, "options": {"displayMode": "lcd", "maxVizHeight": 300, "minVizHeight": 16, "minVizWidth": 8, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "panelId": 24, "refId": "A"}], "title": "Keyspace", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["keyspace_hits", "keyspace_misses"]}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"keyspace_hits": "Hits", "keyspace_misses": "Misses"}}}], "type": "bargauge"}, {"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-blue", "value": null}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 7}, "id": 34, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "panelId": 8, "refId": "A"}], "title": "Eviction Policy", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["maxmemory_policy"]}}}], "type": "stat"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 10}, "id": 32, "panels": [], "repeat": "redis", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Other", "type": "row"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "color-text"}, "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Total duration"}, "properties": [{"id": "custom.width", "value": 99}, {"id": "unit", "value": "s"}, {"id": "decimals", "value": 1}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Client"}, "properties": [{"id": "custom.width", "value": 112}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Idle time"}, "properties": [{"id": "custom.width", "value": 95}, {"id": "unit", "value": "s"}, {"id": "decimals", "value": 0}]}]}, "gridPos": {"h": 15, "w": 6, "x": 0, "y": 11}, "id": 4, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Idle time"}]}, "pluginVersion": "10.3.3", "targets": [{"command": "clientList", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "type": "command"}], "title": "Client connections", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["addr", "age", "idle", "cmd"]}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"addr": "Client", "age": "Total duration", "cmd": "Last command", "id": "Id", "idle": "Idle time"}}}], "type": "table"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "color-text"}, "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Calls"}, "properties": [{"id": "unit", "value": "short"}, {"id": "decimals", "value": 1}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Number of calls"}, "properties": [{"id": "unit", "value": "short"}, {"id": "custom.width", "value": 127}, {"id": "decimals", "value": 1}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Duration"}, "properties": [{"id": "custom.width", "value": 127}, {"id": "decimals", "value": 1}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration per call"}, "properties": [{"id": "decimals", "value": 1}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Command"}, "properties": [{"id": "custom.width", "value": 115}]}]}, "gridPos": {"h": 15, "w": 7, "x": 6, "y": 11}, "id": 41, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Total Duration"}]}, "pluginVersion": "10.3.3", "targets": [{"command": "info", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "section": "commandstats", "type": "command"}], "title": "Command statistics", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Calls": "Number of calls", "Command": "", "Usec": "Total Duration", "Usec_per_call": "Duration per call"}}}], "type": "table"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "color-text"}, "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unitScale": true}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Unique progressive identifier"}, "properties": [{"id": "custom.width", "value": 205}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Timestamp"}, "properties": [{"id": "custom.width", "value": 145}, {"id": "unit", "value": "dateTimeFromNow"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration"}, "properties": [{"id": "custom.width", "value": 92}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Command"}, "properties": [{"id": "custom.width", "value": 1185}]}]}, "gridPos": {"h": 15, "w": 11, "x": 13, "y": 11}, "id": 11, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "10.3.3", "targets": [{"command": "slowlogGet", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "type": "command"}], "title": "Slow queries log", "transformations": [{"id": "organize", "options": {"excludeByName": {"Id": true, "Timestamp": false}, "indexByName": {"Command": 4, "Duration": 3, "Id": 0, "Timestamp": 1, "Timestamp * 1000": 2}, "renameByName": {"Duration": "", "Id": "Id", "Timestamp * 1000": "Timestamp"}}}], "type": "table"}], "refresh": "1m", "schemaVersion": 39, "tags": ["redis"], "templating": {"list": [{"current": {"selected": false, "text": "redis-datasource", "value": "f7bc7175-a145-4d82-b1fc-231eb40ffff5"}, "hide": 0, "includeAll": false, "label": "Redis", "multi": false, "name": "redis", "options": [], "query": "redis-datasource", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Redis Stats", "uid": "RpSjVqWMz", "version": 7, "weekStart": ""}