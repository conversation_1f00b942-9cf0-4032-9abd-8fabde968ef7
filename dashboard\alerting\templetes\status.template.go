{{ define "status.title" }}
{{- if eq .Status "firing" -}}
🔥 [{{ .Status | toUpper }}:{{ len .Alerts }}] 堵 车 了 ! ! !
{{- else if eq .Status "resolved" -}}
✅ [{{ .Status | toUpper }}:{{ len .Alerts }}] 通 车 了 ~ ~ ~
{{- end -}}
{{- end -}}

{{ define "status.print_alert" -}}
{{ if .Annotations -}}
Summary:
{{ .Annotations.summary }}
{{ end -}}
{{ if .DashboardURL -}}
Go to Dashboard: https://grafana.yakumaru.ai/d/f6e0410a-2940-4556-a35b-e81ba4e7ece2/kenta-scheduling-status
{{- end }}
{{- end -}}

{{ define "status.message" -}}
{{ if .Alerts.Firing -}}
{{ range .Alerts.Firing }}
{{ template "status.print_alert" . }}
{{ end -}}
{{ end -}}
{{ if .Alerts.Resolved -}}
{{ range .Alerts.Resolved }}
{{ template "status.print_alert" .}}
{{ end -}}
{{ end -}}
{{- end -}}