resource "grafana_rule_group" "rule_group_f0fc2991d45236f5" {
  org_id           = 1
  name             = "node"
  folder_uid       = "ce170c3f-2355-4db0-8b06-d12c642b8b5d"
  interval_seconds = 60

  rule {
    name      = "Linux Nodes Memory Usage"
    condition = "C"

    data {
      ref_id = "A"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "d31c6998-358e-44d3-8542-ba13b1d024a5"
      model          = "{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"d31c6998-358e-44d3-8542-ba13b1d024a5\"},\"editorMode\":\"code\",\"expr\":\"(100 * (node_memory_MemTotal_bytes{hostname=~\\\"(.+)\\\"} - node_memory_MemFree_bytes{hostname=~\\\"(.+)\\\"} - node_memory_Buffers_bytes{hostname=~\\\"(.+)\\\"} - node_memory_Cached_bytes{hostname=~\\\"(.+)\\\"}) / node_memory_MemTotal_bytes{hostname=~\\\"(.+)\\\"}) / 100\",\"format\":\"time_series\",\"instant\":false,\"interval\":\"\",\"intervalFactor\":2,\"intervalMs\":15000,\"legendFormat\":\"{{hostname}} | {{instance}}\",\"maxDataPoints\":43200,\"range\":true,\"refId\":\"A\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\"}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"A\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"reducer\":\"last\",\"refId\":\"B\",\"type\":\"reduce\"}"
    }
    data {
      ref_id = "C"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.95],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"C\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\",\"unloadEvaluator\":{\"params\":[0.9],\"type\":\"lt\"}}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"B\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"C\",\"type\":\"threshold\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Error"
    for            = "1m"
    annotations = {
      __dashboardUid__ = "000000014"
      __panelId__      = "5"
      summary          = "Memory usage for [ {{ index $labels \"hostname\" }} ] -> [ {{ index $labels \"instance\" }} ] has exceeded 95% for the last 1 minutes: {{ humanizePercentage $values.B.Value }}\n=============================="
    }
    labels = {
      mon_type = "node"
    }
    is_paused = false
  }
  rule {
    name      = "Linux Nodes Disk Usage"
    condition = "C"

    data {
      ref_id = "A"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "d31c6998-358e-44d3-8542-ba13b1d024a5"
      model          = "{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"d31c6998-358e-44d3-8542-ba13b1d024a5\"},\"editorMode\":\"code\",\"expr\":\"(100.0 - 100 * ((node_filesystem_avail_bytes{mountpoint=\\\"/\\\", hostname=~\\\"(.+)\\\"} / 1000 / 1000 ) / (node_filesystem_size_bytes{mountpoint=\\\"/\\\", hostname=~\\\"(.+)\\\"} / 1024 / 1024))) / 100\",\"format\":\"time_series\",\"instant\":false,\"interval\":\"\",\"intervalFactor\":2,\"intervalMs\":15000,\"legendFormat\":\"{{hostname}} | {{instance}}: (mountpoint = {{mountpoint}})\",\"maxDataPoints\":43200,\"range\":true,\"refId\":\"A\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\"}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"A\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"reducer\":\"last\",\"refId\":\"B\",\"type\":\"reduce\"}"
    }
    data {
      ref_id = "C"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.9],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"C\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\",\"unloadEvaluator\":{\"params\":[0.9],\"type\":\"lt\"}}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"B\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"C\",\"type\":\"threshold\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Error"
    for            = "1m"
    annotations = {
      __dashboardUid__ = "000000014"
      __panelId__      = "1"
      summary          = "Disk usage for [ {{ index $labels \"hostname\" }} ] -> [ {{ index $labels \"instance\" }} ] has exceeded 90% for the last 1 minutes: {{ humanizePercentage $values.B.Value }}\n=============================="
    }
    labels = {
      mon_type = "node"
    }
    is_paused = false
  }
  rule {
    name      = "Linux Nodes CPU Usage"
    condition = "C"

    data {
      ref_id = "A"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "d31c6998-358e-44d3-8542-ba13b1d024a5"
      model          = "{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"d31c6998-358e-44d3-8542-ba13b1d024a5\"},\"editorMode\":\"code\",\"expr\":\"1 - (avg by (hostname, instance) (irate(node_cpu_seconds_total{mode=\\\"idle\\\", hostname=~\\\"(.+)\\\"}[1m])))\",\"format\":\"time_series\",\"instant\":false,\"interval\":\"\",\"intervalFactor\":1,\"intervalMs\":15000,\"legendFormat\":\"{{hostname}} | {{instance}}\",\"maxDataPoints\":43200,\"range\":true,\"refId\":\"A\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\"}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"A\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"reducer\":\"last\",\"refId\":\"B\",\"type\":\"reduce\"}"
    }
    data {
      ref_id = "C"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.95],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"C\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\",\"unloadEvaluator\":{\"params\":[0.9],\"type\":\"lt\"}}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"B\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"C\",\"type\":\"threshold\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Error"
    for            = "1m"
    annotations = {
      __dashboardUid__ = "000000014"
      __panelId__      = "6"
      summary          = "CPU usage for [ {{ index $labels \"hostname\" }} ] -> [ {{ index $labels \"instance\" }} ] has exceeded 95% for the last 1 minutes: {{ humanizePercentage $values.B.Value }}\n=============================="
    }
    labels = {
      mon_type = "node"
    }
    is_paused = false
  }
  rule {
    name      = "Linux Nodes Sys Load (15m)"
    condition = "C"

    data {
      ref_id = "A"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "d31c6998-358e-44d3-8542-ba13b1d024a5"
      model          = "{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"d31c6998-358e-44d3-8542-ba13b1d024a5\"},\"editorMode\":\"code\",\"exemplar\":false,\"expr\":\"node_load15{hostname=~\\\"(.+)\\\"}\",\"instant\":false,\"interval\":\"\",\"intervalMs\":15000,\"legendFormat\":\"{{hostname}} | {{instance}}\",\"maxDataPoints\":43200,\"range\":true,\"refId\":\"A\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"B\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\"}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"A\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"reducer\":\"last\",\"refId\":\"B\",\"type\":\"reduce\"}"
    }
    data {
      ref_id = "C"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[15],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"C\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\",\"unloadEvaluator\":{\"params\":[12],\"type\":\"lt\"}}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"B\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"C\",\"type\":\"threshold\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Error"
    for            = "1m"
    annotations = {
      __dashboardUid__ = "000000014"
      __panelId__      = "8"
      summary          = "Linux Nodes Sys Load (15m) for [ {{ index $labels \"hostname\" }} ] -> [ {{ index $labels \"instance\" }} ] has exceeded 15 for the last 1 minutes: {{ humanize $values.B.Value }}\n=============================="
    }
    labels = {
      mon_type = "node"
    }
    is_paused = false
  }
  rule {
    name      = "Inodes Usage"
    condition = "C"

    data {
      ref_id = "A"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "d31c6998-358e-44d3-8542-ba13b1d024a5"
      model          = "{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"d31c6998-358e-44d3-8542-ba13b1d024a5\"},\"editorMode\":\"code\",\"expr\":\"(node_filesystem_files{mountpoint=\\\"/\\\", hostname=~\\\"(.+)\\\",device!~'rootfs'} - node_filesystem_files_free{mountpoint=\\\"/\\\", hostname=~\\\"(.+)\\\",device!~'rootfs'}) / node_filesystem_files{mountpoint=\\\"/\\\", hostname=~\\\"(.+)\\\",device!~'rootfs'}\",\"instant\":false,\"interval\":\"\",\"intervalMs\":15000,\"legendFormat\":\"{{hostname}} | {{instance}}: (mountpoint = {{mountpoint}})\",\"maxDataPoints\":43200,\"range\":true,\"refId\":\"A\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"A\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\"}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"A\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"reducer\":\"last\",\"refId\":\"B\",\"type\":\"reduce\"}"
    }
    data {
      ref_id = "C"

      relative_time_range {
        from = 10800
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0.8],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"C\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\",\"unloadEvaluator\":{\"params\":[0.8],\"type\":\"lt\"}}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"B\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"C\",\"type\":\"threshold\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Error"
    for            = "5m"
    annotations = {
      __dashboardUid__ = "000000014"
      __panelId__      = "17"
      summary          = "Inodes usage for [ {{ index $labels \"hostname\" }} ] -> [ {{ index $labels \"instance\" }} ] has exceeded 80% for the last 5 minutes: {{ humanizePercentage $values.B.Value }}\n=============================="
    }
    labels = {
      mon_type = "node"
    }
    is_paused = false
  }
}
resource "grafana_rule_group" "rule_group_3bec9b60d4761e82" {
  org_id           = 1
  name             = "redis"
  folder_uid       = "ce170c3f-2355-4db0-8b06-d12c642b8b5d"
  interval_seconds = 20

  rule {
    name      = "Redis Resource Status"
    condition = "C"

    data {
      ref_id = "A"

      relative_time_range {
        from = 1800
        to   = 0
      }

      datasource_uid = "d31c6998-358e-44d3-8542-ba13b1d024a5"
      model          = "{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"d31c6998-358e-44d3-8542-ba13b1d024a5\"},\"editorMode\":\"code\",\"exemplar\":false,\"expr\":\"1 - (avg by (hostname, instance) (irate(node_cpu_seconds_total{mode=\\\"idle\\\", projectname=~\\\"yakumaru\\\", hostname=~\\\"(p-redis.+)\\\"}[1m])))\",\"format\":\"time_series\",\"instant\":true,\"interval\":\"\",\"intervalFactor\":1,\"intervalMs\":15000,\"legendFormat\":\"{{hostname}} | {{instance}}\",\"maxDataPoints\":43200,\"range\":false,\"refId\":\"A\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 600
        to   = 0
      }

      datasource_uid = "d31c6998-358e-44d3-8542-ba13b1d024a5"
      model          = "{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"d31c6998-358e-44d3-8542-ba13b1d024a5\"},\"editorMode\":\"code\",\"expr\":\"(100 * (node_memory_MemTotal_bytes{projectname=~\\\"yakumaru\\\", hostname=~\\\"(p-redis.+)\\\"} - node_memory_MemFree_bytes{projectname=~\\\"yakumaru\\\", hostname=~\\\"(p-redis.+)\\\"} - node_memory_Buffers_bytes{projectname=~\\\"yakumaru\\\", hostname=~\\\"(p-redis.+)\\\"} - node_memory_Cached_bytes{projectname=~\\\"yakumaru\\\", hostname=~\\\"(p-redis.+)\\\"}) / node_memory_MemTotal_bytes{projectname=~\\\"yakumaru\\\", hostname=~\\\"(p-redis.+)\\\"}) / 100\",\"instant\":true,\"intervalMs\":1000,\"legendFormat\":\"__auto\",\"maxDataPoints\":43200,\"range\":false,\"refId\":\"B\"}"
    }
    data {
      ref_id = "C"

      relative_time_range {
        from = 600
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0,0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[]},\"reducer\":{\"params\":[],\"type\":\"avg\"},\"type\":\"query\"}],\"datasource\":{\"name\":\"Expression\",\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"($A > 0.85) || ($B > 0.9)\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"C\",\"type\":\"math\"}"
    }
    data {
      ref_id = "D"

      relative_time_range {
        from = 600
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0,0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[]},\"reducer\":{\"params\":[],\"type\":\"avg\"},\"type\":\"query\"}],\"datasource\":{\"name\":\"Expression\",\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"C\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"D\",\"type\":\"threshold\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Error"
    for            = "20s"
    annotations = {
      __dashboardUid__ = "f6e0410a-2940-4556-a35b-e81ba4e7ece2"
      __panelId__      = "14"
      summary          = "1. Redis -> CPU：\n    [ CPU (Threshold: 85%) ] -> 在过去20秒的值为：[ {{ humanizePercentage $values.A.Value }} ]\n----------\n2. Redis -> MEM：\n    [ MEM (Threshold: 90%) ] -> 在过去20秒的值为：[ {{ humanizePercentage $values.B.Value }} ]\n=============================="
    }
    labels = {
      mon_type = "redis"
    }
    is_paused = false
  }
}
resource "grafana_rule_group" "rule_group_dd82c8c2d4285e13" {
  org_id           = 1
  name             = "status"
  folder_uid       = "ce170c3f-2355-4db0-8b06-d12c642b8b5d"
  interval_seconds = 30

  rule {
    name      = "Scheduling Status"
    condition = "G"

    data {
      ref_id = "A"

      relative_time_range {
        from = 1800
        to   = 0
      }

      datasource_uid = "d31c6998-358e-44d3-8542-ba13b1d024a5"
      model          = "{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"d31c6998-358e-44d3-8542-ba13b1d024a5\"},\"disableTextWrap\":false,\"editorMode\":\"code\",\"expr\":\"kenta_scheduling_status{status=\\\"queued\\\"}\",\"format\":\"time_series\",\"fullMetaSearch\":false,\"includeNullMetadata\":true,\"instant\":false,\"intervalMs\":1000,\"legendFormat\":\"__auto\",\"maxDataPoints\":43200,\"range\":true,\"refId\":\"A\",\"useBackend\":false}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 600
        to   = 0
      }

      datasource_uid = "d31c6998-358e-44d3-8542-ba13b1d024a5"
      model          = "{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"d31c6998-358e-44d3-8542-ba13b1d024a5\"},\"disableTextWrap\":false,\"editorMode\":\"code\",\"expr\":\"kenta_scheduling_status{status=\\\"pt\\\"}\",\"format\":\"time_series\",\"fullMetaSearch\":false,\"includeNullMetadata\":true,\"instant\":false,\"intervalMs\":1000,\"legendFormat\":\"__auto\",\"maxDataPoints\":43200,\"range\":true,\"refId\":\"B\",\"useBackend\":false}"
    }
    data {
      ref_id = "C"

      relative_time_range {
        from = 600
        to   = 0
      }

      datasource_uid = "d31c6998-358e-44d3-8542-ba13b1d024a5"
      model          = "{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"d31c6998-358e-44d3-8542-ba13b1d024a5\"},\"editorMode\":\"code\",\"exemplar\":false,\"expr\":\"kenta_scheduling_status{status=\\\"merge\\\"}\",\"format\":\"time_series\",\"instant\":false,\"intervalMs\":1000,\"legendFormat\":\"__auto\",\"maxDataPoints\":43200,\"range\":true,\"refId\":\"C\"}"
    }
    data {
      ref_id = "G"

      relative_time_range {
        from = 600
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0,0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[]},\"reducer\":{\"params\":[],\"type\":\"avg\"},\"type\":\"query\"}],\"datasource\":{\"name\":\"Expression\",\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"(($D > 60.0) && ($D > $E)) || ($F > 100.0)\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"G\",\"type\":\"math\"}"
    }
    data {
      ref_id = "H"

      relative_time_range {
        from = 600
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0,0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[]},\"reducer\":{\"params\":[],\"type\":\"avg\"},\"type\":\"query\"}],\"datasource\":{\"name\":\"Expression\",\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"G\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"H\",\"type\":\"threshold\"}"
    }
    data {
      ref_id = "D"

      relative_time_range {
        from = 1800
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0,0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[]},\"reducer\":{\"params\":[],\"type\":\"avg\"},\"type\":\"query\"}],\"datasource\":{\"name\":\"Expression\",\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"A\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"reducer\":\"last\",\"refId\":\"D\",\"type\":\"reduce\"}"
    }
    data {
      ref_id = "E"

      relative_time_range {
        from = 600
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0,0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[]},\"reducer\":{\"params\":[],\"type\":\"avg\"},\"type\":\"query\"}],\"datasource\":{\"name\":\"Expression\",\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"B\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"reducer\":\"last\",\"refId\":\"E\",\"type\":\"reduce\"}"
    }
    data {
      ref_id = "F"

      relative_time_range {
        from = 600
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[0,0],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[]},\"reducer\":{\"params\":[],\"type\":\"avg\"},\"type\":\"query\"}],\"datasource\":{\"name\":\"Expression\",\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"C\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"reducer\":\"last\",\"refId\":\"F\",\"type\":\"reduce\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Error"
    for            = "30s"
    annotations = {
      __dashboardUid__ = "f6e0410a-2940-4556-a35b-e81ba4e7ece2"
      __panelId__      = "2"
      summary          = "1. 调度状态：\n    [ Queued > PT ]\n    [ Queued ] -> 在过去30秒的值为：[ {{ $values.D.Value }} ]\n----------\nOR\n----------\n2. 调度状态：\n    [ Merge ] -> 在过去30秒的值为：[ {{ $values.F.Value }} ]\n=============================="
    }
    labels = {
      mon_type = "status"
    }
    is_paused = false
  }
  rule {
    name      = "MongoDB Disk Read IOPS"
    condition = "B"

    data {
      ref_id = "A"

      relative_time_range {
        from = 3600
        to   = 0
      }

      datasource_uid = "d31c6998-358e-44d3-8542-ba13b1d024a5"
      model          = "{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"d31c6998-358e-44d3-8542-ba13b1d024a5\"},\"editorMode\":\"code\",\"exemplar\":true,\"expr\":\"label_replace( sum(rate(hardware_disk_metrics_read_count{group_id=~\\\"(.+)\\\", cl_name=~\\\"aws-bureau\\\", instance=~\\\"(.+)\\\"}[30s])) by (instance, disk_name) , \\\"hostname\\\", \\\"$1\\\", \\\"instance\\\", \\\"(.*)\\\")\",\"instant\":true,\"interval\":\"\",\"intervalMs\":15000,\"legendFormat\":\"disk - {{disk_name}} host - {{hostname}} \",\"maxDataPoints\":43200,\"range\":false,\"refId\":\"A\"}"
    }
    data {
      ref_id = "B"

      relative_time_range {
        from = 3600
        to   = 0
      }

      datasource_uid = "__expr__"
      model          = "{\"conditions\":[{\"evaluator\":{\"params\":[2500],\"type\":\"gt\"},\"operator\":{\"type\":\"and\"},\"query\":{\"params\":[\"C\"]},\"reducer\":{\"params\":[],\"type\":\"last\"},\"type\":\"query\"}],\"datasource\":{\"type\":\"__expr__\",\"uid\":\"__expr__\"},\"expression\":\"A\",\"intervalMs\":1000,\"maxDataPoints\":43200,\"refId\":\"B\",\"type\":\"threshold\"}"
    }

    no_data_state  = "NoData"
    exec_err_state = "Error"
    for            = "1m"
    annotations = {
      __dashboardUid__ = "KsE2_DWSz"
      __panelId__      = "76"
      summary          = "MongoDB Instance: [ {{ index $labels \"instance\" }} ]\nMongoDB Disk Read IOPS: [ {{ humanize $values.A.Value }} ]"
    }
    labels = {
      mon_type = "mongo"
    }
    is_paused = false
  }
}
