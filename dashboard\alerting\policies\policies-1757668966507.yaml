apiVersion: 1
policies:
    - orgId: 1
      receiver: NeoX-Node
      group_by:
        - grafana_folder
        - alertname
      routes:
        - receiver: NeoX-Node
          object_matchers:
            - - mon_type
              - =
              - node
          continue: true
          group_wait: 30s
          group_interval: 3m
          repeat_interval: 4h
        - receiver: NeoX-Status
          object_matchers:
            - - mon_type
              - =
              - status
          continue: true
          group_wait: 30s
          group_interval: 3m
          repeat_interval: 4h
        - receiver: NeoX-MongoDB
          object_matchers:
            - - mon_type
              - =
              - mongo
          continue: true
          group_wait: 30s
          group_interval: 3m
          repeat_interval: 4h
        - receiver: NeoX-Redis
          object_matchers:
            - - mon_type
              - =
              - redis
          continue: true
          group_wait: 30s
          group_interval: 3m
          repeat_interval: 4h
        - receiver: NeoX-Disk
          object_matchers:
            - - mon_type
              - =
              - disk
          continue: true
          group_wait: 30s
          group_interval: 3m
          repeat_interval: 4h
        - receiver: NeoX-Probe
          object_matchers:
            - - mon_type
              - =
              - probe
          continue: true
          group_wait: 30s
          group_interval: 3m
          repeat_interval: 4h
        - receiver: NeoX-Domain
          object_matchers:
            - - mon_type
              - =
              - domain
          continue: true
          group_wait: 30s
          group_interval: 3m
          repeat_interval: 4h
        - receiver: NeoX-Certs
          object_matchers:
            - - mon_type
              - =
              - certs
          continue: true
          group_wait: 30s
          group_interval: 3m
          repeat_interval: 4h
