{{ define "node_disk.title" }}
{{- if eq .Status "firing" -}}
❗ [{{ .Status | toUpper }}:{{ len .Alerts }}] Node Disk Usage 有些高了 ! ! !
{{- else if eq .Status "resolved" -}}
✅ [{{ .Status | toUpper }}:{{ len .Alerts }}] Node Disk Usage 降下来了 ~ ~ ~
{{- end -}}
{{- end -}}

{{ define "node_disk.print_alert" -}}
{{ if .Annotations -}}
Summary:
{{ .Annotations.summary }}
{{ end -}}
{{ if .DashboardURL -}}
Go to Dashboard: https://grafana.yakumaru.ai/d/000000014/alerts-linux-nodes
{{- end }}
{{- end -}}

{{ define "node_disk.message" -}}
{{ if .Alerts.Firing -}}
{{ range .Alerts.Firing }}
{{ template "node_disk.print_alert" . }}
{{ end -}}
{{ end -}}
{{ if .Alerts.Resolved -}}
{{ range .Alerts.Resolved }}
{{ template "node_disk.print_alert" .}}
{{ end -}}
{{ end -}}
{{- end -}}