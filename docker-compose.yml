version: "3.8"

services:
  grafana:
    image: grafana/grafana:${GRAFANA_VERSION}
    container_name: grafana
    restart: unless-stopped
    user: "0:0"
    privileged: true
    ports:
      - "3001:3000"
    environment:
  #    GF_SECURITY_ADMIN_USER: admin
  #    GF_SECURITY_ADMIN_PASSWORD: Admin@Grafana
  #    GF_USERS_ALLOW_SIGN_UP: false
      GF_PATHS_PROVISIONING: /etc/grafana/provisioning
      GF_AUTH_ANONYMOUS_ENABLED: "true"
      GF_AUTH_ANONYMOUS_ORG_NAME: NeoX
      GF_AUTH_ANONYMOUS_ORG_ROLE: Admin
      GF_FEATURE_TOGGLES_ENABLE: alertingSimplifiedRouting,alertingQueryAndExpressionsStepMode
    entrypoint:
      - sh
      - -euc
      - |
        mkdir -p /etc/grafana/provisioning/datasources
        cat <<EOF > /etc/grafana/provisioning/datasources/ds.yaml
        apiVersion: 1
        datasources:
        - name: Loki
          type: loki
          access: proxy 
          orgId: 1
          url: http://loki:3100
          basicAuth: false
          isDefault: true
          version: 1
          editable: false
        EOF
        /run.sh
    volumes:
      - "./grafana/data:/var/lib/grafana"
      - "./grafana/grafana.ini:/etc/grafana/grafana.ini"

  prometheus:
    image: prom/prometheus:${PROM_VERSION}
    container_name: prometheus
    restart: unless-stopped
    user: "0:0"
    privileged: true
    ports:
      - "9091:9090"
    volumes:
      - "./prometheus/config/prometheus.yml:/etc/prometheus/prometheus.yml:rw"
      - "./prometheus/config/node-exporter.yml:/etc/prometheus/node-exporter.yml:rw"
      - "./prometheus/config/cadvisor.yml:/etc/prometheus/cadvisor.yml:rw"
      - "./prometheus/config/blackbox-exporter.yml:/etc/prometheus/blackbox-exporter.yml:rw"
      - "./prometheus/data:/prometheus"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--storage.tsdb.retention.time=30d'

  node-exporter:
    image: prom/node-exporter:${NODE_EXPORTER_VERSION}
    container_name: node-exporter
    # user: "0:0"
    volumes:
      - "/proc:/host/proc:ro"
      - "/sys:/host/sys:ro"
      - "/:/rootfs:ro"
      - "/etc/localtime:/etc/localtime"
    # ports:
    #   - "9111:9111"
    command:
      - '--web.listen-address=:9111'
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
    labels:
      org.label-schema.group: "monitoring"
    restart: unless-stopped
    network_mode: host
  
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:${CADVISOR_VERSION}
    container_name: cadvisor
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
      - /etc/localtime:/etc/localtime
    # ports:
    #   - "9112:8080"
    command:
      - '--port=9112'
      - '--docker_only=true'
    labels:
      org.label-schema.group: "monitoring"
    restart: unless-stopped
    network_mode: host
    privileged: true
    # cap_add:
    # - SYS_ADMIN
    # - SYS_PTRACE
    # - SYS_RESOURCE
    # - NET_ADMIN
    # - DAC_READ_SEARCH
    healthcheck:
      disable: true
      # test: ["CMD", "curl", "-f", "http://localhost:9112/healthz"]
      # interval: 30s
      # timeout: 10s
      # retries: 3

  blackbox-exporter:
    image: quay.io/prometheus/blackbox-exporter:${BLACKBOX_EXPORTER_VERSION}
    container_name: blackbox-exporter
    restart: unless-stopped
    ports:
      - "9115:9115"
    volumes:
      - "./blackbox/blackbox.yml:/etc/blackbox/blackbox.yml"
    command:
      - '--config.file=/etc/blackbox/blackbox.yml'
  
  domain-exporter:
    image: ghcr.io/caarlos0/domain_exporter:${DOMAIN_EXPORTER_VERSION}
    container_name: domain-exporter
    restart: unless-stopped
    ports:
      - "9222:9222"
    volumes:
      - "./domain/domains.yml:/etc/domain-exporter/domains.yml"
    environment:
      - CACHE_TTL=${WHOIS_CACHE_TTL}
    command:
      - '--config=/etc/domain-exporter/domains.yml'

  redis:
    image: redis/redis-stack:latest
    container_name: redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      ### production
      # - "./service/redis/data:/data"
      # - "./service/redis/conf/redis-monitor.conf:/redis-stack.conf"
      ### dev & testing
      - "./redis/data:/data"
      - "./redis/conf/redis.conf:/redis-stack.conf"
    # command: ["redis-server"]

### Loki Services >>
  loki:
    image: grafana/loki:${LOKI_VERSION}
    container_name: loki
    restart: unless-stopped
    user: "1000:1000"
    ports:
      - "3100:3100"
    volumes:
      - "./loki/config/loki-config.yaml:/etc/loki/loki-config.yaml"
      - "./loki/data/retention:/data/retention"
    command: '-config.file=/etc/loki/loki-config.yaml'

  promtail:
    image: grafana/promtail:${PROMTAIL_VERSION}
    container_name: promtail
    restart: unless-stopped
    user: "1000:1000"
    volumes:
      - "./loki/log:/var/log"
      - "./loki/config/promtail-config.yaml:/etc/promtail/promtail-config.yaml"
      - "/mnt/efs/production/www/neox-med-backend/storage/logs:/applogs/medical-backend:ro" # 如果这样映射，那么 promtail-config.yaml 中的 __path__ 需要改为 /applogs/medical-backend/p_merge*log
      - "/mnt/efs/production/devops/logs:/applogs/semaphore:ro"
      - "/mnt/efs/production/gpu/log:/applogs/gpu:ro"
      - "./loki/tmp:/tmp" # positions.yaml 文件记录了 Promtail 读取日志文件的位置,将 tmp 持久化可以避免 Promtail 容器重启后文件丢失导致 Promtail 从头开始读取所有日志文件，大量重复日志被发送到 Loki。
    command: '-config.file=/etc/promtail/promtail-config.yaml'
### << Loki Services

  postgres:
    image: timescale/timescaledb:latest-pg13
    container_name: timescaledb
    restart: unless-stopped
    # networks:
    #   - timescale-net
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres@neox
      POSTGRES_DB: perf
      TZ: Asia/Tokyo
    volumes:
      # - "timescale_data:/var/lib/postgresql/data"
      - "./timescaledb/data:/var/lib/postgresql/data"
      - "./timescaledb/sql/01_timescale_schema_locust-plugins.sql:/docker-entrypoint-initdb.d/01_timescale_schema_locust-plugins.sql"
      - "./timescaledb/sql/02_zz_hypertable_locust-plugins.sql:/docker-entrypoint-initdb.d/02_zz_hypertable_locust-plugins.sql"

networks:
  default:
    external: true
    name: ${NETWORK_NAME}
