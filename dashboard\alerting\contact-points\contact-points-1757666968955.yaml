apiVersion: 1
contactPoints:
    - orgId: 1
      name: NeoX-Certs
      receivers:
        - uid: dexu31o4wivi8c
          type: wecom
          settings:
            message: '{{ template "certs.message" . }}'
            msgtype: text
            title: '{{ template "certs.title" . }}'
            url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e1438159-fd30-4532-adf1-44e008abf04f
          disableResolveMessage: false
    - orgId: 1
      name: NeoX-Disk
      receivers:
        - uid: dewuhan2j0zr4c
          type: wecom
          settings:
            message: '{{ template "node_disk.message" . }}'
            msgtype: text
            title: '{{ template "node_disk.title" . }}'
            url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3da4a5b7-16e4-4ff6-8139-ed194e19b379
          disableResolveMessage: false
    - orgId: 1
      name: NeoX-Domain
      receivers:
        - uid: aexu2xgphmcxsd
          type: wecom
          settings:
            message: '{{ template "domain.message" . }}'
            msgtype: text
            title: '{{ template "domain.title" . }}'
            url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e1438159-fd30-4532-adf1-44e008abf04f
          disableResolveMessage: false
    - orgId: 1
      name: NeoX-MongoDB
      receivers:
        - uid: a3f49164-fc0a-489b-9d68-e5a7502f71ec
          type: wecom
          settings:
            message: '{{ template "mongo.message" . }}'
            msgtype: text
            title: '{{ template "mongo.title" . }}'
            url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3da4a5b7-16e4-4ff6-8139-ed194e19b379
          disableResolveMessage: false
    - orgId: 1
      name: NeoX-Node
      receivers:
        - uid: a316bace-67a5-41b3-9d16-96c250307032
          type: wecom
          settings:
            message: '{{ template "slack.message" . }}'
            msgtype: text
            url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=507191c2-433c-4f79-b308-92b2a51c41a7
          disableResolveMessage: false
    - orgId: 1
      name: NeoX-Probe
      receivers:
        - uid: cexn840ojg83ke
          type: wecom
          settings:
            message: '{{ template "probe.message" . }}'
            msgtype: text
            title: '{{ template "probe.title" . }}'
            url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3da4a5b7-16e4-4ff6-8139-ed194e19b379
          disableResolveMessage: false
    - orgId: 1
      name: NeoX-Redis
      receivers:
        - uid: b04236a3-9f47-4eee-bdbe-fce5caa48446
          type: wecom
          settings:
            agent_id: admin
            message: '{{ template "redis.message" . }}'
            msgtype: text
            title: '{{ template "redis.title" . }}'
            url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=507191c2-433c-4f79-b308-92b2a51c41a7
          disableResolveMessage: false
    - orgId: 1
      name: NeoX-Status
      receivers:
        - uid: a867ae1b-722e-4500-bd1c-8ad524c257d7
          type: wecom
          settings:
            message: '{{ template "status.message" . }}'
            msgtype: text
            title: '{{ template "status.title" . }}'
            touser: '@all'
            url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3da4a5b7-16e4-4ff6-8139-ed194e19b379
          disableResolveMessage: false
