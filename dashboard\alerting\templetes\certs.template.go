{{ define "certs.title" }}
{{- if eq .Status "firing" -}}
💰 [{{ .Status | toUpper }}:{{ len .Alerts }}] 证书快要到期了 ! ! !
{{- else if eq .Status "resolved" -}}
✅ [{{ .Status | toUpper }}:{{ len .Alerts }}] 证书续期完毕了 ~ ~ ~
{{- end -}}
{{- end -}}

{{ define "certs.print_alert" -}}
{{ if .Annotations -}}
Summary:
{{ .Annotations.summary }}
{{ end -}}
{{ if .DashboardURL -}}
Go to Dashboard: https://grafana.yakumaru.ai/d/xtkCtBkiz/neox-domains-and-certs-monitor
{{- end }}
{{- end -}}

{{ define "certs.message" -}}
{{ if .Alerts.Firing -}}
{{ range .Alerts.Firing }}
{{ template "certs.print_alert" . }}
{{ end -}}
{{ end -}}
{{ if .Alerts.Resolved -}}
{{ range .Alerts.Resolved }}  
{{ template "certs.print_alert" .}}
{{ end -}}
{{ end -}}
{{- end -}}