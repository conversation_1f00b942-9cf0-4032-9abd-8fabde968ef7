{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "MongoDB Atlas Hardware Metrics collected with official prometheus integration.", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 19655, "graphTooltip": 0, "id": 28, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Overview", "type": "row"}, {"columns": [], "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "fontSize": "100%", "gridPos": {"h": 3, "w": 24, "x": 0, "y": 1}, "id": 4, "options": {"frameIndex": 0, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Value #A"}]}, "pluginVersion": "8.2.2", "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "hidden"}, {"alias": "", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": false, "expr": "label_replace( sum(mongodb_up{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by ( group_id, org_id, rs_nm, cl_name), \"hostname\", \"$1\", \"instance\", \"(.*)\")\n", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Group Metadata", "transform": "table", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value #A": true, "instance": true, "rs_nm": false}, "indexByName": {"Time": 0, "Value #A": 6, "cl_name": 4, "group_id": 3, "org_id": 1, "rs_nm": 5}, "renameByName": {"Time": "", "cl_name": "Cluster Name", "group_id": "Group Id", "hostname": "Host", "instance": "", "org_id": "Org Id", "process_port": "Port", "replica_state": "ReplicaSet State", "rs_nm": "ReplicaSet Name"}}}, {"id": "groupBy", "options": {"fields": {"Cluster Name": {"aggregations": [], "operation": "groupby"}, "Group Id": {"aggregations": [], "operation": "groupby"}, "Group Name ": {"aggregations": [], "operation": "groupby"}, "Host": {"aggregations": [], "operation": "groupby"}, "Host ": {"aggregations": [], "operation": "groupby"}, "Org Id": {"aggregations": [], "operation": "groupby"}, "Port": {"aggregations": [], "operation": "groupby"}, "Replica set state": {"aggregations": [], "operation": "groupby"}, "ReplicaSet Name": {"aggregations": [], "operation": "groupby"}, "ReplicaSet State": {"aggregations": [], "operation": "groupby"}, "host ": {"aggregations": [], "operation": "groupby"}, "hostname": {"aggregations": [], "operation": "groupby"}, "instance": {"aggregations": [], "operation": "groupby"}, "port": {"aggregations": [], "operation": "groupby"}, "process_port": {"aggregations": [], "operation": "groupby"}, "replica set": {"aggregations": [], "operation": "groupby"}, "replica set state": {"aggregations": [], "operation": "groupby"}, "replica_state": {"aggregations": [], "operation": "groupby"}, "rs_nm": {"aggregations": [], "operation": "groupby"}}}}], "type": "table-old"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "decimals": 2, "displayName": "", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 5, "w": 24, "x": 0, "y": 4}, "id": 6, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": false, "expr": "label_replace( sum(mongodb_info{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance, replica_state_name, process_port, rs_nm, process_type), \"hostname\", \"$1\", \"instance\", \"(.*):.*\")", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Cluster host list", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "Value #A": true, "instance": false, "process_type": false, "rs_nm": false}, "indexByName": {"Time": 0, "Value #A": 6, "hostname": 1, "instance": 2, "process_port": 3, "replica_state": 4, "rs_nm": 5}, "renameByName": {"Time": "", "Value": "", "hostname": "Host", "instance": "Hostname", "process_port": "Port", "process_type": "Type", "replica_state": "ReplicaSet State", "replica_state_name": "Replica State Name", "rs_nm": "ReplicaSet Name"}}}, {"id": "groupBy", "options": {"fields": {"Host": {"aggregations": [], "operation": "groupby"}, "Host ": {"aggregations": [], "operation": "groupby"}, "Hostname": {"aggregations": [], "operation": "groupby"}, "Port": {"aggregations": [], "operation": "groupby"}, "Replica State Name": {"aggregations": [], "operation": "groupby"}, "Replica set state": {"aggregations": [], "operation": "groupby"}, "ReplicaSet Name": {"aggregations": [], "operation": "groupby"}, "ReplicaSet State": {"aggregations": [], "operation": "groupby"}, "Type": {"aggregations": [], "operation": "groupby"}, "host ": {"aggregations": [], "operation": "groupby"}, "hostname": {"aggregations": [], "operation": "groupby"}, "instance": {"aggregations": [], "operation": "groupby"}, "port": {"aggregations": [], "operation": "groupby"}, "process_port": {"aggregations": [], "operation": "groupby"}, "process_type": {"aggregations": [], "operation": "groupby"}, "replica set": {"aggregations": [], "operation": "groupby"}, "replica set state": {"aggregations": [], "operation": "groupby"}, "replica_state": {"aggregations": [], "operation": "groupby"}, "replica_state_name": {"aggregations": [], "operation": "groupby"}, "rs_nm": {"aggregations": [], "operation": "groupby"}}}}, {"id": "merge", "options": {"reducers": []}}], "type": "table"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 8, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "System Memory", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of kilobytes of used shared memory (shared between several processes, thus including RAM disks, SYS-V-IPC and BSD like SHMEM)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "deckbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 10}, "id": 10, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(hardware_system_memory_shared_mem_kilobytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System Memory - Shared", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of kilobytes of buffer cache, relatively temporary storage for raw disk blocks", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "deckbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 10}, "id": 12, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(hardware_system_memory_buffers_kilobytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System Memory - Buffers", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of kilobytes in the page cache.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "deckbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 10}, "id": 14, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace (sum(hardware_system_memory_cached_kilobytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System Memory - Cached", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The number of kilobytes of physical memory in use\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "deckbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 18}, "id": 16, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(hardware_system_memory_mem_total_kilobytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance) - sum(hardware_system_memory_mem_free_kilobytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System Memory - Used", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The total amount of swap space in free and used, measured in kilobytes\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "deckbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 18}, "id": 18, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(hardware_system_memory_swap_total_kilobytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance) - sum(hardware_system_memory_swap_free_kilobytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "swap used for host - {{hostname}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace (sum(hardware_system_memory_swap_free_kilobytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "hide": false, "interval": "", "legendFormat": "swap free for host - {{hostname}}", "refId": "B"}], "title": "System Memory - Swap", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "An estimate of the number of kilobytes of system memory available for running new applications, without swapping", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "deckbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 18}, "id": 20, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(hardware_system_memory_mem_available_kilobytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System Memory - Available", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 22, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "System CPU", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU had something runnable, but the hypervisor chose to run something else. For servers with more than 1 CPU core, this value can exceed 100%.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 27}, "id": 24, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_steal_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / 10) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System cpu - steal", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent servicing guest, which is included in user. For servers with more than 1 CPU core, this value can exceed 100%.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 27}, "id": 26, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_guest_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / 10) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System cpu - guest", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent performing software interrupts. For servers with more than 1 CPU core, this value can exceed 100%.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 27}, "id": 28, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_soft_irq_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / 10) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System cpu - softirq", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent performing hardware interrupts. For servers with more than 1 CPU core, this value can exceed 100%.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 27}, "id": 30, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_irq_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / 10) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System cpu - irq", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent waiting for IO operations to complete. For servers with more than 1 CPU core, this value can exceed 100%\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 34}, "id": 32, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(rate(hardware_system_cpu_io_wait_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / 10) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System cpu - iowait", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent occupied by all processes with a positive nice value. For servers with more than 1 CPU core, this value can exceed 100%.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 34}, "id": 34, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_nice_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / 10) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System cpu - nice", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent servicing operating system calls from all processes. For servers with more than 1 CPU core, this value can exceed 100%.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 34}, "id": 36, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_kernel_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / 10) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System cpu - kernel", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent servicing all user applications (not just MongoDB processes). For servers with more than 1 CPU core, this value can exceed 100%.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 34}, "id": 38, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_user_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / 10) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "System cpu - user", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 41}, "id": 82, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Process CPU", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent servicing this MongoDB process, scaled to a range of 0-100% by dividing by the number of CPU cores.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 42}, "id": 84, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace((sum(rate(hardware_process_cpu_user_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\", process_port=~\"$process_port\" } [$interval])) by (instance, process_port)  / on(instance) group_left  hardware_platform_num_logical_cpus{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\" })/10, \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}} port - {{process_port}}", "refId": "A"}], "title": "Normalized Process cpu - user", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 42}, "id": 86, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace((sum(rate(hardware_process_cpu_children_user_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\", process_port=~\"$process_port\" } [$interval])) by (instance, process_port)  / on(instance) group_left  hardware_platform_num_logical_cpus{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\" })/10, \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}} port - {{process_port}}", "refId": "A"}], "title": "Normalized Process cpu - children user", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent servicing operating system calls for this MongoDB process, scaled to a range of 0-100% by dividing by the number of CPU cores.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 42}, "id": 88, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace((sum(rate(hardware_process_cpu_kernel_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\", process_port=~\"$process_port\" } [$interval])) by (instance, process_port)  / on(instance) group_left  hardware_platform_num_logical_cpus{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\" })/10, \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}} port - {{process_port}}", "refId": "A"}], "title": "Normalized Process cpu - kernel", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 42}, "id": 90, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace((sum(rate(hardware_process_cpu_children_kernel_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\", process_port=~\"$process_port\" } [$interval])) by (instance, process_port)  / on(instance) group_left  hardware_platform_num_logical_cpus{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\" })/10, \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}} port - {{process_port}}", "refId": "A"}], "title": "Normalized Process cpu - children kernel", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent servicing this MongoDB process. For servers with more than 1 CPU core, this value can exceed 100%.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 49}, "id": 92, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(rate(hardware_process_cpu_user_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\", process_port=~\"$process_port\" } [$interval])) by (instance, process_port)  / 10, \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}} port - {{process_port}}", "refId": "A"}], "title": "Process cpu - user", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 49}, "id": 94, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(rate(hardware_process_cpu_children_user_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\", process_port=~\"$process_port\" } [$interval])) by (instance, process_port)  / 10, \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}} port - {{process_port}}", "refId": "A"}], "title": "Process cpu - child user", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent servicing operating system calls for this MongoDB process. For servers with more than 1 CPU core, this value can exceed 100%.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 49}, "id": 96, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(rate(hardware_process_cpu_kernel_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\", process_port=~\"$process_port\" } [$interval])) by (instance, process_port)  / 10, \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}} port - {{process_port}}", "refId": "A"}], "title": "Process cpu - kernel", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 49}, "id": 98, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(rate(hardware_process_cpu_children_kernel_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\", process_port=~\"$process_port\" } [$interval])) by (instance, process_port)  / 10, \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}} port - {{process_port}}", "refId": "A"}], "title": "Process cpu - children kernel", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 56}, "id": 40, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Normalized System CPU", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU had something runnable, but the hypervisor chose to run something else. It is scaled to a range of 0-100% by dividing by the number of CPU cores.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 57}, "id": 42, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_steal_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / (10 * hardware_platform_num_logical_cpus{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"})) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "Normalized System cpu - steal", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent servicing guest, which is included in user. It is scaled to a range of 0-100% by dividing by the number of CPU cores.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 57}, "id": 44, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_guest_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / (10 * hardware_platform_num_logical_cpus{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"})) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "Normalized System cpu - guest", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent performing software interrupts. It is scaled to a range of 0-100% by dividing by the number of CPU cores.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 57}, "id": 46, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_soft_irq_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / (10 * hardware_platform_num_logical_cpus{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"})) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "Normalized System cpu - softirq", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent performing hardware interrupts. It is scaled to a range of 0-100% by dividing by the number of CPU cores.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 57}, "id": 48, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_irq_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / (10 * hardware_platform_num_logical_cpus{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"})) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "Normalized System cpu - irq", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent waiting for IO operations to complete. It is scaled to a range of 0-100% by dividing by the number of CPU cores.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 64}, "id": 50, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace(sum(rate(hardware_system_cpu_io_wait_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / (10 * hardware_platform_num_logical_cpus{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"})) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "Normalized System cpu - iowait", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent occupied by all processes with a positive nice value. It is scaled to a range of 0-100% by dividing by the number of CPU cores.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 64}, "id": 52, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_nice_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / (10 * hardware_platform_num_logical_cpus{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"})) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "Normalized System cpu - nice", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent servicing operating system calls from all processes. It is scaled to a range of 0-100% by dividing by the number of CPU cores.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 64}, "id": 54, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_kernel_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / (10 * hardware_platform_num_logical_cpus{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"})) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "Normalized System cpu - kernel", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time the CPU spent servicing all user applications (not just MongoDB processes). It is scaled to a range of 0-100% by dividing by the number of CPU cores.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 64}, "id": 56, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_cpu_user_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / (10 * hardware_platform_num_logical_cpus{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"})) by (instance) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "host - {{hostname}}", "refId": "A"}], "title": "Normalized System cpu - user", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 71}, "id": 58, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "System Network", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average rate of physical bytes received per second by the eth0 network interface\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps", "unitScale": true}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 72}, "id": 60, "options": {"legend": {"calcs": ["min", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_network_eth0_bytes_in_bytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval])) by (instance) + sum(rate(hardware_system_network_lo_bytes_in_bytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval])) by (instance), \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "rx - {{hostname}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "expr": "label_replace( -1 * sum(rate(hardware_system_network_eth0_bytes_out_bytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval])) by (instance) + sum(rate(hardware_system_network_lo_bytes_out_bytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval])) by (instance), \"hostname\", \"$1\", \"instance\", \"(.*)\")", "legendFormat": "tx - {{hostname}}", "refId": "B"}], "title": "Network traffic", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 81}, "id": 62, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "System Disk", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The total bytes of free disk space on the disk partition used by MongoDB.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 82}, "id": 64, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(hardware_disk_metrics_disk_space_free_bytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance, disk_name) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "disk - {{disk_name}}, host - {{hostname}} ", "refId": "A"}], "title": "System Disk - Free", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percent of free disk space on the partition used by MongoDB.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 82}, "id": 66, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(hardware_disk_metrics_disk_space_free_bytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"} / (hardware_disk_metrics_disk_space_used_bytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"} + hardware_disk_metrics_disk_space_free_bytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) * 100) by (instance, disk_name) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "disk - {{disk_name}}, host - {{hostname}} ", "refId": "A"}], "title": "System Disk Percent Free", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The total bytes of used disk space on the partition that runs MongoDB.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 82}, "id": 68, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(hardware_disk_metrics_disk_space_used_bytes{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}) by (instance, disk_name) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "disk - {{disk_name}} host - {{hostname}} ", "refId": "A"}], "title": "System Disk - Used", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The write latency in milliseconds of the disk partition used by MongoDB.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 90}, "id": 72, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_disk_metrics_write_time_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / rate(hardware_disk_metrics_write_count{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval])) by (instance, disk_name), \"hostname\", \"$1\", \"instance\", \"(.*)\")", "hide": false, "interval": "", "legendFormat": "disk - {{disk_name}} host - {{hostname}} ", "refId": "B"}], "title": "System Disk Write Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The Read latency in milliseconds of the disk partition used by MongoDB.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 90}, "id": 74, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_disk_metrics_read_time_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) / rate(hardware_disk_metrics_read_count{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval])) by (instance, disk_name), \"hostname\", \"$1\", \"instance\", \"(.*)\")", "hide": false, "interval": "", "legendFormat": "disk - {{disk_name}} host - {{hostname}} ", "refId": "B"}], "title": "System Disk Read Latency", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The read throughput of I/O operations per second for the disk partition used for MongoDB.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/ sec", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 90}, "id": 76, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_disk_metrics_read_count{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval])) by (instance, disk_name) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "interval": "", "legendFormat": "disk - {{disk_name}} host - {{hostname}} ", "refId": "A"}], "title": "System Disk Read IOPS", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The average length of queue of requests issued to the disk partition used by MongoDB.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 98}, "id": 80, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_disk_metrics_weighted_time_io_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) ) by (instance, DiskName), \"hostname\", \"$1\", \"instance\", \"(.*)\")", "hide": false, "interval": "", "legendFormat": "disk - {{disk_name}} host - {{hostname}} ", "refId": "B"}], "title": "System Disk Queue Depth", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The write throughput of I/O operations per second for the disk partition used for MongoDB.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/ sec", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 98}, "id": 70, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_disk_metrics_write_count{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval])) by (instance, disk_name) , \"hostname\", \"$1\", \"instance\", \"(.*)\")", "hide": false, "interval": "", "legendFormat": "disk - {{disk_name}} host - {{hostname}} ", "refId": "B"}], "title": "System Disk Write IOPS", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The percentage of time during which requests are being issued to and serviced by the partition. This includes requests from any process, not just MongoDB processes.\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 98}, "id": 78, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_disk_metrics_total_time_milliseconds{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) ) by (instance, disk_name) / 10, \"hostname\", \"$1\", \"instance\", \"(.*)\")", "hide": false, "interval": "", "legendFormat": "disk - {{disk_name}} host - {{hostname}} ", "refId": "B"}], "title": "System Disk Util %", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 106}, "id": 100, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Misc", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "description": "The total number of pages swapped in and out per second\n", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "/ sec", "unitScale": true}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 107}, "id": 102, "options": {"legend": {"calcs": ["min", "max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_vm_page_swap_in{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) ) by (instance), \"hostname\", \"$1\", \"instance\", \"(.*)\")", "hide": false, "interval": "", "legendFormat": "swap in for host - {{hostname}} ", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "exemplar": true, "expr": "label_replace( sum(rate(hardware_system_vm_page_swap_out{group_id=~\"$group_id\", cl_name=~\"$cl_name\", instance=~\"$host.*\"}[$interval]) ) by (instance), \"hostname\", \"$1\", \"instance\", \"(.*)\")", "hide": false, "interval": "", "legendFormat": "swap out for host - {{hostname}} ", "refId": "A"}], "title": "System VM Swap IO", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "prometheus", "value": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "hide": 0, "includeAll": false, "multi": false, "name": "Datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": false, "text": "Medical-mongo-metrics", "value": "Medical-mongo-metrics"}, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "definition": "label_values(mongodb_up, job)", "hide": 0, "includeAll": false, "multi": false, "name": "job", "options": [], "query": {"query": "label_values(mongodb_up, job)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "622816f117b23174b353b2e3", "value": "622816f117b23174b353b2e3"}, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "definition": "label_values(mongodb_up{job=\"$job\"}, group_id)", "hide": 0, "includeAll": false, "label": "Group Id", "multi": false, "name": "group_id", "options": [], "query": {"query": "label_values(mongodb_up{job=\"$job\"}, group_id)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "aws-bureau", "value": "aws-bureau"}, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "definition": "label_values(mongodb_up{group_id='$group_id'}, cl_name)", "hide": 0, "includeAll": false, "label": "Cluster Name", "multi": false, "name": "cl_name", "options": [], "query": {"query": "label_values(mongodb_up{group_id='$group_id'}, cl_name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "definition": "label_values(mongodb_up{group_id='$group_id', cl_name='$cl_name'}, rs_nm)", "hide": 0, "includeAll": true, "label": "Replica set name", "multi": true, "name": "rs_nm", "options": [], "query": {"query": "label_values(mongodb_up{group_id='$group_id', cl_name='$cl_name'}, rs_nm)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "definition": "label_values(mongodb_up{group_id='$group_id', cl_name='$cl_name',rs_nm='$rs_nm'},instance)", "hide": 0, "includeAll": true, "multi": true, "name": "host", "options": [], "query": {"query": "label_values(mongodb_up{group_id='$group_id', cl_name='$cl_name',rs_nm='$rs_nm'},instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "definition": "label_values(mongodb_up{group_id='$group_id', cl_name='$cl_name',rs_nm='$rs_nm'},process_port)", "description": "Only applicable for process level metrics", "hide": 0, "includeAll": true, "label": "Process Port", "multi": true, "name": "process_port", "options": [], "query": {"query": "label_values(mongodb_up{group_id='$group_id', cl_name='$cl_name',rs_nm='$rs_nm'},process_port)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"auto": true, "auto_count": 30, "auto_min": "10s", "current": {"selected": false, "text": "30s", "value": "30s"}, "hide": 0, "label": "Interval", "name": "interval", "options": [{"selected": false, "text": "auto", "value": "$__auto_interval_interval"}, {"selected": true, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "1d", "value": "1d"}], "query": "30s,1m,5m,30m,1h,1d", "refresh": 2, "skipUrlSync": false, "type": "interval"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "MongoDB Atlas Hardware Metrics", "uid": "KsE2_DWSz", "version": 3, "weekStart": ""}