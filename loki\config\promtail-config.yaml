server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: neox-med-backend
    static_configs:
      - targets:
          - localhost
        labels:
          job: p_merge
          __path__: /applogs/medical-backend/p_merge*log
      - targets:
          - localhost
        labels:
          job: p_error
          __path__: /applogs/medical-backend/p_error*log
      - targets:
          - localhost
        labels:
          job: p_dispatcher
          __path__: /applogs/medical-backend/p_dispatcher*log
      - targets:
          - localhost
        labels:
          job: p_async
          __path__: /applogs/medical-backend/p_async*log
      - targets:
          - localhost
        labels:
          job: neox_data_object
          __path__: /applogs/medical-backend/neox_data_object*log
      - targets:
          - localhost
        labels:
          job: merchant
          __path__: /applogs/medical-backend/merchant*log
      - targets:
          - localhost
        labels:
          job: laravel
          __path__: /applogs/medical-backend/laravel-*log
      - targets:
          - localhost
        labels:
          job: enquete
          __path__: /applogs/medical-backend/enquete*log
      - targets:
          - localhost
        labels:
          job: semaphore
          __path__: /applogs/semaphore/semaphore.log
      - targets:
          - localhost
        labels:
          job: check_semaphore
          __path__: /applogs/semaphore/check_semaphore.log
      - targets:
          - localhost
        labels:
          job: gpu_engine
          __path__: /applogs/gpu/gpu*log
