{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 13, "links": [], "liveNow": false, "panels": [{"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 59, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops", "unitScale": true}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.0.6", "targets": [{"command": "info", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "section": "stats", "streaming": true, "streamingCapacity": 1000, "streamingInterval": 1000, "type": "command"}], "title": "Ops/sec", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["instantaneous_ops_per_sec", "#time"]}}}], "type": "timeseries"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 62, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 9, "w": 9, "x": 12, "y": 0}, "id": 3, "options": {"graph": {}, "legend": {"calcs": ["mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.5.7", "targets": [{"command": "info", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "section": "clients", "streaming": true, "streamingCapacity": 1000, "streamingInterval": 1000, "type": "command"}], "title": "Clients", "transformations": [{"id": "organize", "options": {"excludeByName": {"client_recent_max_input_buffer": true, "client_recent_max_output_buffer": true, "maxclients": true}, "indexByName": {}, "renameByName": {"blocked_clients": "Pending on a blocking call ", "clients_in_timeout_table": "Clients in the timeout table", "cluster_connections": "Cluster connections", "connected_clients": "Client connections", "tracking_clients": "Clients being tracked"}}}], "type": "timeseries"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-blue", "value": null}]}, "unit": "s", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 0}, "id": 17, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"command": "info", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "section": "server", "streaming": true, "streamingCapacity": 1, "type": "command"}], "title": "Uptime", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["uptime_in_seconds"]}}}], "type": "stat"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-blue", "value": null}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 3}, "id": 19, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"command": "info", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "section": "server", "streaming": true, "streamingCapacity": 1, "type": "command"}], "title": "Version", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["redis_version"]}}}], "type": "stat"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "dark-blue", "value": null}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 3, "w": 3, "x": 21, "y": 6}, "id": 21, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "/.*/", "values": true}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.3.3", "targets": [{"command": "info", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "section": "memory", "streaming": true, "streamingCapacity": 1, "type": "command"}], "title": "Eviction Policy", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["maxmemory_policy"]}}}], "type": "stat"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 13, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Statistics", "type": "row"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 53, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes", "unitScale": true}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 10}, "id": 7, "options": {"graph": {}, "legend": {"calcs": ["mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.5.7", "targets": [{"command": "info", "datasource": {"uid": "$redis"}, "query": "", "refId": "A", "section": "memory", "streaming": true, "streamingCapacity": 1000, "streamingInterval": 1000, "type": "command"}], "title": "Memory", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["used_memory", "used_memory_rss", "used_memory_peak", "total_system_memory", "used_memory_lua", "maxmemory", "#time"]}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"maxmemory": "Memory Limit", "total_system_memory": "Total System Memory", "used_memory": "Used Memory", "used_memory_lua": "Used Memory, LUA", "used_memory_peak": "Used Memory, Peak", "used_memory_rss": "Used Memory, RSS"}}}], "type": "timeseries"}, {"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 47, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "KBs", "unitScale": true}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 10}, "id": 5, "options": {"graph": {}, "legend": {"calcs": ["mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.5.7", "targets": [{"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "panelId": 2, "refId": "A"}], "title": "Network", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["instantaneous_input_kbps", "instantaneous_output_kbps", "#time"]}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"instantaneous_input_kbps": "Input", "instantaneous_output_kbps": "Output"}}}], "type": "timeseries"}, {"datasource": {"uid": "$redis"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 49, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 19}, "id": 9, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "7.5.7", "targets": [{"command": "", "datasource": {"uid": "$redis"}, "query": "dbsize", "refId": "A", "streaming": true, "streamingCapacity": 1000, "streamingInterval": 1000, "type": "cli"}], "title": "Number of Keys", "type": "timeseries"}, {"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 48, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 19}, "id": 11, "options": {"graph": {}, "legend": {"calcs": ["mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "7.5.7", "targets": [{"datasource": {"type": "datasource", "uid": "-- Dashboard --"}, "panelId": 2, "refId": "A"}], "title": "Keys Statistics", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["expired_keys", "evicted_keys", "#time"]}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"evicted_keys": "Evicted <PERSON>", "expired_keys": "Expired Keys"}}}], "type": "timeseries"}], "refresh": "", "schemaVersion": 39, "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "redis-datasource", "value": "f7bc7175-a145-4d82-b1fc-231eb40ffff5"}, "hide": 0, "includeAll": false, "label": "Redis", "multi": false, "name": "redis", "options": [], "query": "redis-datasource", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Redis Streaming", "uid": "E_yGWieGz", "version": 2, "weekStart": ""}