{{ define "redis.title" }}
{{- if eq .Status "firing" -}}
❗ [{{ .Status | toUpper }}:{{ len .Alerts }}] Redis Resource 有些高了 ! ! !
{{- else if eq .Status "resolved" -}}
✅ [{{ .Status | toUpper }}:{{ len .Alerts }}] Redis Resource 降下来了 ~ ~ ~
{{- end -}}
{{- end -}}

{{ define "redis.print_alert" -}}
{{ if .Annotations -}}
Summary:
{{ .Annotations.summary }}
{{ end -}}
{{ if .DashboardURL -}}
Go to Dashboard: https://grafana.yakumaru.ai/d/f6e0410a-2940-4556-a35b-e81ba4e7ece2/kenta-scheduling-status
{{- end }}
{{- end -}}

{{ define "redis.message" -}}
{{ if .Alerts.Firing -}}
{{ range .Alerts.Firing }}
{{ template "redis.print_alert" . }}
{{ end -}}
{{ end -}}
{{ if .Alerts.Resolved -}}
{{ range .Alerts.Resolved }}
{{ template "redis.print_alert" .}}
{{ end -}}
{{ end -}}
{{- end -}}