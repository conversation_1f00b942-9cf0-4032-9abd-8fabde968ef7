resource "grafana_contact_point" "contact_point_372e4d4abea9b1c1" {
  name = "NeoX-Certs"

  wecom {
    url      = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e1438159-fd30-4532-adf1-44e008abf04f"
    message  = "{{ template \"certs.message\" . }}"
    title    = "{{ template \"certs.title\" . }}"
    msg_type = "text"
  }
}
resource "grafana_contact_point" "contact_point_08b818e9d25a102f" {
  name = "NeoX-Disk"

  wecom {
    url      = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3da4a5b7-16e4-4ff6-8139-ed194e19b379"
    message  = "{{ template \"node_disk.message\" . }}"
    title    = "{{ template \"node_disk.title\" . }}"
    msg_type = "text"
  }
}
resource "grafana_contact_point" "contact_point_f65bbe9b0a9339c4" {
  name = "NeoX-Domain"

  wecom {
    url      = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e1438159-fd30-4532-adf1-44e008abf04f"
    message  = "{{ template \"domain.message\" . }}"
    title    = "{{ template \"domain.title\" . }}"
    msg_type = "text"
  }
}
resource "grafana_contact_point" "contact_point_0d8b004061f2f9ce" {
  name = "NeoX-MongoDB"

  wecom {
    url      = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3da4a5b7-16e4-4ff6-8139-ed194e19b379"
    message  = "{{ template \"mongo.message\" . }}"
    title    = "{{ template \"mongo.title\" . }}"
    msg_type = "text"
  }
}
resource "grafana_contact_point" "contact_point_5f5dc1ea036fe2a4" {
  name = "NeoX-Node"

  wecom {
    url      = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=507191c2-433c-4f79-b308-92b2a51c41a7"
    message  = "{{ template \"slack.message\" . }}"
    msg_type = "text"
  }
}
resource "grafana_contact_point" "contact_point_9983aeb5922f1d8e" {
  name = "NeoX-Probe"

  wecom {
    url      = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3da4a5b7-16e4-4ff6-8139-ed194e19b379"
    message  = "{{ template \"probe.message\" . }}"
    title    = "{{ template \"probe.title\" . }}"
    msg_type = "text"
  }
}
resource "grafana_contact_point" "contact_point_61707ec7ad239169" {
  name = "NeoX-Redis"

  wecom {
    url      = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=507191c2-433c-4f79-b308-92b2a51c41a7"
    agent_id = "admin"
    message  = "{{ template \"redis.message\" . }}"
    title    = "{{ template \"redis.title\" . }}"
    msg_type = "text"
  }
}
resource "grafana_contact_point" "contact_point_4b37f70cadcae756" {
  name = "NeoX-Status"

  wecom {
    url      = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3da4a5b7-16e4-4ff6-8139-ed194e19b379"
    message  = "{{ template \"status.message\" . }}"
    title    = "{{ template \"status.title\" . }}"
    msg_type = "text"
    to_user  = "@all"
  }
}
