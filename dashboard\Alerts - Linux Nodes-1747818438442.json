{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Alerts for Linux Nodes using prometheus and node_exporter. You can have alerts for Disk space, CPU and Memory. Also added a log of alerts and alert status.", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 5984, "graphTooltip": 0, "id": 4, "links": [{"asDropdown": false, "icon": "dashboard", "includeVars": false, "keepTime": true, "tags": [], "targetBlank": true, "title": "<PERSON><PERSON>", "tooltip": "Open Scheduling Status Dashboard", "type": "link", "url": "/d/f6e0410a-2940-4556-a35b-e81ba4e7ece2/kenta-scheduling-status"}, {"asDropdown": false, "icon": "dashboard", "includeVars": false, "keepTime": true, "tags": [], "targetBlank": true, "title": "Node Info Full", "tooltip": "", "type": "link", "url": "/d/rYdddlPWk/node-info-full"}, {"asDropdown": false, "icon": "dashboard", "includeVars": false, "keepTime": true, "tags": [], "targetBlank": true, "title": "Redis Stats", "tooltip": "Open Redis Stats Dashboard", "type": "link", "url": "/d/RpSjVqWMz/redis-stats"}, {"asDropdown": false, "icon": "external link", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "Bureau", "tooltip": "Open Bureau in new tab", "type": "link", "url": "https://medical-bureau-ci.moair.net/"}], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 9, "panels": [], "title": "<PERSON><PERSON><PERSON>", "type": "row"}, {"dashboardFilter": "", "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 1}, "id": 2, "limit": 10, "links": [], "nameFilter": "", "onlyAlertsOnDashboard": false, "options": {"alertInstanceLabelFilter": "", "alertName": "", "dashboardAlerts": false, "groupBy": [], "groupMode": "default", "maxItems": 20, "sortOrder": 1, "stateFilter": {"error": true, "firing": true, "noData": false, "normal": true, "pending": true}, "viewMode": "list"}, "show": "current", "sortOrder": 1, "stateFilter": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "Alerts Status", "transparent": true, "type": "alertlist"}, {"dashboardFilter": "", "datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "gridPos": {"h": 9, "w": 16, "x": 8, "y": 1}, "id": 3, "limit": "5", "links": [], "nameFilter": "", "onlyAlertsOnDashboard": false, "options": {"alertInstanceLabelFilter": "", "alertName": "", "dashboardAlerts": false, "groupBy": [], "groupMode": "default", "maxItems": 20, "sortOrder": 1, "stateFilter": {"error": true, "firing": true, "noData": false, "normal": true, "pending": true}, "viewMode": "list"}, "show": "changes", "sortOrder": 1, "stateFilter": [], "targets": [{"datasource": {"type": "prometheus", "uid": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "refId": "A"}], "title": "<PERSON><PERSON><PERSON>", "transparent": true, "type": "alertlist"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 10}, "id": 10, "panels": [], "title": "Sys Load", "type": "row"}, {"alert": {"conditions": [{"evaluator": {"params": [70], "type": "gt"}, "operator": {"type": "or"}, "query": {"params": ["C", "10m", "now"]}, "reducer": {"params": [], "type": "min"}, "type": "query"}, {"evaluator": {"params": [70], "type": "gt"}, "operator": {"type": "or"}, "query": {"params": ["A", "10m", "now"]}, "reducer": {"params": [], "type": "min"}, "type": "query"}, {"evaluator": {"params": [70], "type": "gt"}, "operator": {"type": "or"}, "query": {"params": ["D", "10m", "now"]}, "reducer": {"params": [], "type": "min"}, "type": "query"}], "executionErrorState": "alerting", "for": "0m", "frequency": "60s", "handler": 1, "message": "CPU Usage Alert", "name": "Linux Nodes CPU Usage alert", "noDataState": "no_data", "notifications": []}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "decimals": 0, "fieldMinMax": false, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 12, "w": 8, "x": 0, "y": 11}, "id": 4, "links": [], "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": false, "expr": "node_load1{hostname=~\"$hostname\"}", "hide": false, "instant": false, "legendFormat": "{{hostname}} | {{instance}}", "range": true, "refId": "A"}], "title": "Linux Nodes Sys Load (1m)", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "decimals": 0, "fieldMinMax": false, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 12, "w": 8, "x": 8, "y": 11}, "id": 7, "links": [], "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": false, "expr": "node_load5{hostname=~\"$hostname\"}", "hide": false, "instant": false, "legendFormat": "{{hostname}} | {{instance}}", "range": true, "refId": "A"}], "title": "Linux Nodes Sys Load (5m)", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "decimals": 0, "fieldMinMax": false, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 15}]}, "unit": "short", "unitScale": true}, "overrides": []}, "gridPos": {"h": 12, "w": 8, "x": 16, "y": 11}, "id": 8, "links": [], "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "exemplar": false, "expr": "node_load15{hostname=~\"$hostname\"}", "hide": false, "instant": false, "legendFormat": "{{hostname}} | {{instance}}", "range": true, "refId": "A"}], "title": "Linux Nodes Sys Load (15m)", "transparent": true, "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 23}, "id": 11, "panels": [], "title": "CPU", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}, {"color": "red", "value": 95}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 24}, "id": 6, "links": [], "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "100 - (avg by (hostname, instance) (irate(node_cpu_seconds_total{mode=\"idle\", hostname=~\"$hostname\"}[1m])) * 100)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{hostname}} | {{instance}}", "range": true, "refId": "A"}], "title": "Linux Nodes CPU Usage", "transparent": true, "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 35}, "id": 12, "panels": [], "title": "MEM", "type": "row"}, {"alert": {"conditions": [{"evaluator": {"params": [90], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "frequency": "60s", "handler": 1, "name": "Linux Nodes Memory Usage alert", "noDataState": "no_data", "notifications": []}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "max": 100, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "red", "value": 95}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 36}, "id": 5, "links": [], "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "100*(node_memory_MemTotal_bytes{hostname=~\"$hostname\"} - node_memory_MemFree_bytes{hostname=~\"$hostname\"} - node_memory_Buffers_bytes{hostname=~\"$hostname\"} - node_memory_Cached_bytes{hostname=~\"$hostname\"}) / node_memory_MemTotal_bytes{hostname=~\"$hostname\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{hostname}} | {{instance}}", "range": true, "refId": "A"}], "title": "Linux Nodes Memory Usage", "transparent": true, "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 47}, "id": 13, "panels": [], "title": "Disk", "type": "row"}, {"alert": {"alertRuleTags": {}, "conditions": [{"evaluator": {"params": [90], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "min"}, "type": "query"}], "executionErrorState": "alerting", "for": "0m", "frequency": "60s", "handler": 1, "message": "Disk Space Alert", "name": "Linux Nodes Disk Usage alert", "noDataState": "no_data", "notifications": []}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "links": [], "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "red", "value": 90}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 48}, "id": 1, "links": [], "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "100.0 - 100 * ((node_filesystem_avail_bytes{mountpoint=\"$mountpoint\", hostname=~\"$hostname\"} / 1000 / 1000 ) / (node_filesystem_size_bytes{mountpoint=\"$mountpoint\", hostname=~\"$hostname\"} / 1024 / 1024))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{hostname}} | {{instance}}: (mountpoint = {{mountpoint}})", "range": true, "refId": "A"}], "title": "Linux Nodes Disk Usage", "transparent": true, "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 59}, "id": 14, "panels": [], "title": "Filesystem", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "Usage = (Inodes - IFree) / Inodes * 100", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "decimals": 2, "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent", "unitScale": true}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 60}, "id": 17, "links": [], "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "(node_filesystem_files{mountpoint=\"$mountpoint\", hostname=~\"$hostname\",device!~'rootfs'} - node_filesystem_files_free{mountpoint=\"$mountpoint\", hostname=~\"$hostname\",device!~'rootfs'}) / node_filesystem_files{mountpoint=\"$mountpoint\", hostname=~\"$hostname\",device!~'rootfs'} * 100", "hide": false, "instant": false, "legendFormat": "{{hostname}} | {{instance}}: (mountpoint = {{mountpoint}})", "range": true, "refId": "B"}], "title": "File Nodes Usage", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "df -i 的 Inodes 数量", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 73}, "id": 15, "links": [], "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "node_filesystem_files{mountpoint=\"$mountpoint\", hostname=~\"$hostname\",device!~'rootfs'}", "hide": false, "instant": false, "legendFormat": "{{hostname}} | {{instance}}: (mountpoint = {{mountpoint}})", "range": true, "refId": "B"}], "title": "File Nodes Size", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "df -i 的 IUsed 数量", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 86}, "id": 18, "links": [], "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "node_filesystem_files{mountpoint=\"$mountpoint\", hostname=~\"$hostname\",device!~'rootfs'} - node_filesystem_files_free{mountpoint=\"$mountpoint\", hostname=~\"$hostname\",device!~'rootfs'}", "hide": false, "instant": false, "legendFormat": "{{hostname}} | {{instance}}: (mountpoint = {{mountpoint}})", "range": true, "refId": "B"}], "title": "File Nodes Used", "transparent": true, "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${datasource}"}, "description": "df -i 的 IFree 数量", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unitScale": true}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 99}, "id": 16, "links": [], "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${datasource}"}, "editorMode": "code", "expr": "node_filesystem_files_free{mountpoint=\"$mountpoint\", hostname=~\"$hostname\",device!~'rootfs'}", "hide": false, "instant": false, "legendFormat": "{{hostname}} | {{instance}}: (mountpoint = {{mountpoint}})", "range": true, "refId": "B"}], "title": "File Nodes Free", "transparent": true, "type": "timeseries"}], "refresh": "30s", "schemaVersion": 39, "tags": ["alerts", "linux"], "templating": {"list": [{"current": {"selected": false, "text": "prometheus", "value": "d31c6998-358e-44d3-8542-ba13b1d024a5"}, "hide": 0, "includeAll": false, "label": "Datasource", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": false, "text": "node_exporter", "value": "node_exporter"}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(node_uname_info,job)", "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [], "query": {"qryType": 1, "query": "label_values(node_uname_info,job)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "${datasource}"}, "definition": "label_values(node_uname_info{job=\"$job\"},hostname)", "hide": 0, "includeAll": true, "label": "Name", "multi": true, "name": "hostname", "options": [], "query": {"qryType": 1, "query": "label_values(node_uname_info{job=\"$job\"},hostname)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": true, "text": "/", "value": "/"}, "hide": 0, "label": "Mountpoint", "name": "mountpoint", "options": [{"selected": true, "text": "/", "value": "/"}], "query": "/", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-30m", "to": "now"}, "timeRangeUpdatedDuringEditOrView": false, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Alerts - Linux Nodes", "uid": "000000014", "version": 33, "weekStart": ""}