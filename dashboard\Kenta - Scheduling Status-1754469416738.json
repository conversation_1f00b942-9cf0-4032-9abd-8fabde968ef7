{"__inputs": [{"name": "DS_NEOX-REDIS-MONITOR", "label": "neox-redis-monitor", "description": "", "type": "datasource", "pluginId": "redis-datasource", "pluginName": "Redis"}, {"name": "DS_PROMETHEUS", "label": "prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": {}, "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "12.1.0"}, {"type": "datasource", "id": "loki", "name": "<PERSON>", "version": "12.1.0"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "datasource", "id": "redis-datasource", "name": "Redis", "version": "2.2.0"}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "The monitoring information of load traffic scheduling status.", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [{"asDropdown": false, "icon": "dashboard", "includeVars": true, "keepTime": true, "tags": [], "targetBlank": true, "title": "NeoX Gateway", "tooltip": "Open NeoX Gateway Monitoring Dashboard", "type": "link", "url": "/d/e096fa9f-0221-4987-b073-47b4f0ef48a2/neox-gateway-monitoring"}, {"asDropdown": false, "icon": "dashboard", "includeVars": false, "keepTime": true, "tags": [], "targetBlank": true, "title": "Alerts Linux Nodes", "tooltip": "Open Alerts Linux Nodes Dashboard", "type": "link", "url": "/d/000000014/alerts-linux-nodes"}, {"asDropdown": false, "icon": "dashboard", "includeVars": false, "keepTime": true, "tags": [], "targetBlank": true, "title": "Node Info Full", "tooltip": "", "type": "link", "url": "/d/rYdddlPWk/node-info-full"}, {"asDropdown": false, "icon": "dashboard", "includeVars": false, "keepTime": true, "tags": [], "targetBlank": true, "title": "Redis Stats", "tooltip": "Open Redis Stats Dashboard", "type": "link", "url": "/d/RpSjVqWMz/redis-stats"}, {"asDropdown": false, "icon": "dashboard", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "MongoDB Atlas System", "tooltip": "Open MongoDB Atlas System Dashboard", "type": "link", "url": "/d/W0lo7Gx7z123/mongodb-atlas-system-metrics"}, {"asDropdown": false, "icon": "dashboard", "includeVars": false, "keepTime": false, "tags": [], "targetBlank": true, "title": "MongoDB Atlas Hardware", "tooltip": "Open MongoDB Atlas Hardware Dashboard", "type": "link", "url": "/d/KsE2_DWSz/mongodb-atlas-hardware-metrics"}], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 4, "panels": [], "title": "Summary", "type": "row"}, {"datasource": {"type": "redis-datasource", "uid": "$redis"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "yellow", "mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "dashed"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 60}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "01: New"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "02: <PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "03: Lock"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "04: Process Total"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "05: Process Busy"}, "properties": [{"id": "color", "value": {"fixedColor": "super-light-red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "06: <PERSON><PERSON>"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "07: <PERSON>k"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}]}]}, "gridPos": {"h": 19, "w": 12, "x": 0, "y": 1}, "id": 2, "interval": "1000ms", "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Name", "sortDesc": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.1.0", "targets": [{"aggregation": "last", "bucket": 3000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "keyName": "GRAFANA_TASK_STATUS_NEW", "legend": "01: New", "query": "", "refId": "A", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "last", "bucket": 3000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_QUEUED", "legend": "02: <PERSON><PERSON>", "query": "", "refId": "B", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "last", "bucket": 3000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_LOCK", "legend": "03: Lock", "query": "", "refId": "C", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "last", "bucket": 3000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_PT", "legend": "04: Process Total", "query": "", "refId": "D", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "last", "bucket": 3000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_PB", "legend": "05: Process Busy", "query": "", "refId": "E", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "last", "bucket": 3000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_MERGE", "legend": "06: <PERSON><PERSON>", "query": "", "refId": "F", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "last", "bucket": 3000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_TOOK", "legend": "07: <PERSON>k", "query": "", "refId": "G", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}], "title": "Scheduling Status: All", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}, {"datasource": {"type": "redis-datasource", "uid": "$redis"}, "description": "Day-over-day comparison for mid-week days.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "bars", "fillOpacity": 50, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "处理速度（枚/分）"}, "properties": [{"id": "custom.drawStyle", "value": "line"}, {"id": "custom.axisPlacement", "value": "right"}, {"id": "custom.fillOpacity", "value": 25}, {"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": false}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "每分钟QR识别枚数"}, "properties": [{"id": "custom.drawStyle", "value": "line"}, {"id": "custom.axisPlacement", "value": "right"}, {"id": "custom.fillOpacity", "value": 25}]}]}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 1}, "hideTimeOverride": false, "id": 11, "interval": "1000ms", "links": [{"targetBlank": true, "title": "Bureau - 处方笺枚数", "url": "https://medical-bureau-ci.moair.net/customer/count?type=1"}], "options": {"legend": {"calcs": ["sum", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"aggregation": "last", "bucket": 3600000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "keyName": "GRAFANA_PRES_COUNT_LAST_WEEKDAY", "legend": "上周同日枚数", "query": "", "refId": "A", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "sum", "bucket": 3600000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_PRES_COUNT_TODAY", "legend": "今日枚数", "query": "", "refId": "B", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "last", "bucket": 60000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "hide": false, "keyName": "GRAFANA_PRES_COUNT_TODAY", "legend": "处理速度（枚/分）", "query": "", "refId": "C", "type": "timeSeries"}, {"aggregation": "last", "bucket": 60000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "$redis"}, "fill": true, "hide": false, "keyName": "GRAFANA_PRES_QR_COUNT_TODAY", "legend": "每分钟QR识别枚数", "query": "", "refId": "D", "type": "timeSeries"}], "timeFrom": "now/d", "title": "Prescription Count: Midweek DoD", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}, {"datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "description": "Day-over-day comparison for mid-week days from self-developed OCR engine.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "bars", "fillOpacity": 50, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "处理速度（枚/分）"}, "properties": [{"id": "custom.drawStyle", "value": "line"}, {"id": "custom.axisPlacement", "value": "right"}, {"id": "custom.fillOpacity", "value": 25}, {"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": false}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 11}, "hideTimeOverride": false, "id": 27, "interval": "1000ms", "links": [{"targetBlank": true, "title": "Bureau - 处方笺枚数", "url": "https://medical-bureau-ci.moair.net/customer/count?type=1"}], "options": {"legend": {"calcs": ["sum", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"aggregation": "last", "bucket": 3600000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_SDE_PRES_COUNT_LAST_WEEKDAY", "legend": "上周同日枚数", "query": "", "refId": "A", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "sum", "bucket": 3600000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "keyName": "GRAFANA_NEOX_ENGINE_REQUEST_COUNT", "legend": "今日枚数", "query": "", "refId": "B", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "sum", "bucket": 60000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "$redis"}, "fill": true, "hide": false, "keyName": "GRAFANA_NEOX_ENGINE_REQUEST_COUNT", "legend": "处理速度（枚/分）", "query": "", "refId": "C", "type": "timeSeries"}], "timeFrom": "now/d", "title": "Prescription Count from NeoX OCR Engine: Midweek DoD", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}, {"datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 50, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "fieldMinMax": false, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "OCR 每分钟请求次数"}, "properties": [{"id": "custom.drawStyle", "value": "line"}, {"id": "custom.axisPlacement", "value": "right"}, {"id": "custom.fillOpacity", "value": 25}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "NEOX-OCR 每分钟请求次数"}, "properties": [{"id": "custom.drawStyle", "value": "line"}, {"id": "custom.axisPlacement", "value": "right"}, {"id": "custom.fillOpacity", "value": 25}]}]}, "gridPos": {"h": 13, "w": 12, "x": 0, "y": 20}, "hideTimeOverride": false, "id": 25, "interval": "1m", "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"aggregation": "last", "bucket": 60000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "keyName": "GRAFANA_LINE_REQUEST_TIME", "legend": "OCR 每分钟平均耗时 (秒)", "query": "", "refId": "A", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "last", "bucket": 60000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_LINE_REQUEST_COUNT", "legend": "OCR 每分钟请求次数", "query": "", "refId": "B", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "last", "bucket": 60000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "$redis"}, "fill": true, "hide": false, "keyName": "GRAFANA_NEOX_ENGINE_REQUEST_TIME", "legend": "NEOX-OCR 每分钟平均耗时 (秒)", "query": "", "refId": "C", "type": "timeSeries"}, {"aggregation": "last", "bucket": 60000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "hide": false, "keyName": "GRAFANA_NEOX_ENGINE_REQUEST_COUNT", "legend": "NEOX-OCR 每分钟请求次数", "query": "", "refId": "D", "type": "timeSeries"}], "timeFrom": "now/d", "title": "Summary of OCR Usage", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}, {"datasource": {"type": "redis-datasource", "uid": "$redis"}, "description": "", "fieldConfig": {"defaults": {"color": {"fixedColor": "light-blue", "mode": "shades", "seriesBy": "last"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 50, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "fieldMinMax": false, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 13, "w": 12, "x": 12, "y": 20}, "hideTimeOverride": false, "id": 26, "interval": "1m", "options": {"legend": {"calcs": ["min", "max", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"aggregation": "last", "bucket": 60000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "keyName": "GRAFANA_MERCHANT_ONLINE_COUNT", "legend": "", "query": "", "refId": "A", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "在线店铺数", "zrangeQuery": "BYSCORE"}], "timeFrom": "now/d", "title": "Summary of Merchant", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": 0}, {"color": "red", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 33}, "id": 14, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "100 - (avg by (hostname, instance) (irate(node_cpu_seconds_total{mode=\"idle\", env=~\"$env\", projectname=~\"$projectname\", hostname=~\"$hostname\"}[$time_interval])) * 100)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{hostname}} | {{instance}}", "range": true, "refId": "A"}], "title": "Linux Nodes CPU Usage [ Last ]", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": 0}, {"color": "red", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 46}, "id": 22, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.1.0", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "100 - (avg by (hostname, instance) (rate(node_cpu_seconds_total{mode=\"idle\", env=~\"$env\", projectname=~\"$projectname\", hostname=~\"$hostname\"}[$time_interval])) * 100)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{hostname}} | {{instance}}", "range": true, "refId": "A"}], "title": "Linux Nodes CPU Usage [ Avg ]", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}, {"color": "red", "value": 95}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 13, "w": 24, "x": 0, "y": 59}, "id": 21, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "100*(node_memory_MemTotal_bytes{env=~\"$env\", projectname=~\"$projectname\", hostname=~\"$hostname\"} - node_memory_MemFree_bytes{env=~\"$env\", projectname=~\"$projectname\", hostname=~\"$hostname\"} - node_memory_Buffers_bytes{env=~\"$env\", projectname=~\"$projectname\", hostname=~\"$hostname\"} - node_memory_Cached_bytes{env=~\"$env\", projectname=~\"$projectname\", hostname=~\"$hostname\"}) / node_memory_MemTotal_bytes{env=~\"$env\", projectname=~\"$projectname\", hostname=~\"$hostname\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{hostname}} | {{instance}}", "range": true, "refId": "A"}], "title": "Linux Nodes Memory Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 12, "w": 8, "x": 0, "y": 72}, "id": 16, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "node_load1{env=~\"$env\", projectname=~\"$projectname\", hostname=~\"$hostname\"}", "hide": false, "instant": false, "legendFormat": "{{hostname}} | {{instance}}", "range": true, "refId": "A"}], "title": "Linux Nodes Sys Load (1m)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 12, "w": 8, "x": 8, "y": 72}, "id": 17, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "node_load5{env=~\"$env\", projectname=~\"$projectname\", hostname=~\"$hostname\"}", "hide": false, "instant": false, "legendFormat": "{{hostname}} | {{instance}}", "range": true, "refId": "A"}], "title": "Linux Nodes Sys Load (5m)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "transparent"}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 12, "w": 8, "x": 16, "y": 72}, "id": 18, "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Name", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "node_load15{env=~\"$env\", projectname=~\"$projectname\", hostname=~\"$hostname\"}", "hide": false, "instant": false, "legendFormat": "{{hostname}} | {{instance}}", "range": true, "refId": "A"}], "title": "Linux Nodes Sys Load (15m)", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 84}, "id": 23, "panels": [{"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "description": "All scheduling status with sliding time window. (Sliding Window Size: Last 60s)", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 20}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 16, "w": 24, "x": 0, "y": 85}, "id": 24, "interval": "10s", "options": {"legend": {"calcs": ["max", "mean", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.0.3", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "kenta_scheduling_status_transform", "instant": false, "legendFormat": "{{status}}", "range": true, "refId": "A"}], "title": "Scheduling Status: All", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}], "title": "Scheduling Status Detail (Sliding Time Window)", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 85}, "id": 5, "panels": [{"datasource": {"type": "redis-datasource", "uid": "$redis"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 86}, "id": 13, "interval": "1000ms", "options": {"legend": {"calcs": ["max", "min", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Name", "sortDesc": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.3", "targets": [{"aggregation": "avg", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "keyName": "GRAFANA_TASK_STATUS_NEW", "legend": "[ New: Avg within 10s ]", "query": "", "refId": "A", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "max", "bucket": 10000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_NEW", "legend": "[ New: Max within 10s ]", "query": "", "refId": "B", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "min", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_NEW", "legend": "[ New: Min within 10s ]", "query": "", "refId": "C", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}], "title": "Status: New", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}, {"datasource": {"type": "redis-datasource", "uid": "$redis"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 97}, "id": 6, "interval": "1000ms", "options": {"legend": {"calcs": ["max", "min", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Name", "sortDesc": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.3", "targets": [{"aggregation": "avg", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "keyName": "GRAFANA_TASK_STATUS_QUEUED", "legend": "[ Queued: Avg within 10s ]", "query": "", "refId": "A", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "max", "bucket": 10000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_QUEUED", "legend": "[ Queued: <PERSON> within 10s ]", "query": "", "refId": "B", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "min", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_QUEUED", "legend": "[ Queued: Min within 10s ]", "query": "", "refId": "C", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}], "title": "Status: Queued", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}, {"datasource": {"type": "redis-datasource", "uid": "$redis"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 108}, "id": 7, "interval": "1000ms", "options": {"legend": {"calcs": ["max", "min", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Name", "sortDesc": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.0.3", "targets": [{"aggregation": "avg", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "keyName": "GRAFANA_TASK_STATUS_LOCK", "legend": "[ Lock: Avg within 10s ]", "query": "", "refId": "A", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "max", "bucket": 10000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_LOCK", "legend": "[ Lock: Max within 10s ]", "query": "", "refId": "B", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "min", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_LOCK", "legend": "[ Lock: Min within 10s ]", "query": "", "refId": "C", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}], "title": "Status: Lock", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}, {"datasource": {"type": "redis-datasource", "uid": "$redis"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 0, "y": 119}, "id": 8, "interval": "1000ms", "options": {"legend": {"calcs": ["max", "min", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Name", "sortDesc": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"aggregation": "avg", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "keyName": "GRAFANA_TASK_STATUS_PT", "legend": "[ PT: Avg within 10s ]", "query": "", "refId": "A", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "max", "bucket": 10000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_PT", "legend": "[ PT: Max within 10s ]", "query": "", "refId": "B", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "min", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_PT", "legend": "[ PT: Min within 10s ]", "query": "", "refId": "C", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}], "title": "Status: Process Total", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}, {"datasource": {"type": "redis-datasource", "uid": "$redis"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 119}, "id": 9, "interval": "1000ms", "options": {"legend": {"calcs": ["max", "min", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Name", "sortDesc": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"aggregation": "avg", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "keyName": "GRAFANA_TASK_STATUS_PB", "legend": "[ PB: Avg within 10s ]", "query": "", "refId": "A", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "max", "bucket": 10000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_PB", "legend": "[ PB: Max within 10s ]", "query": "", "refId": "B", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "min", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_PB", "legend": "[ PB: Min within 10s ]", "query": "", "refId": "C", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}], "title": "Status: Process Busy", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}, {"datasource": {"type": "redis-datasource", "uid": "$redis"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 130}, "id": 3, "interval": "1000ms", "options": {"legend": {"calcs": ["max", "min", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Name", "sortDesc": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"aggregation": "avg", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "keyName": "GRAFANA_TASK_STATUS_MERGE", "legend": "[ Merge: Avg within 10s ]", "query": "", "refId": "A", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "max", "bucket": 10000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_MERGE", "legend": "[ Merge: <PERSON> within 10s ]", "query": "", "refId": "B", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "min", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_MERGE", "legend": "[ Merge: Min within 10s ]", "query": "", "refId": "C", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}], "title": "Status: <PERSON><PERSON>", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}, {"datasource": {"type": "redis-datasource", "uid": "$redis"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": true, "axisCenteredZero": false, "axisColorMode": "text", "axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 60, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none", "unitScale": true}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 141}, "id": 10, "interval": "1000ms", "options": {"legend": {"calcs": ["max", "min", "lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Name", "sortDesc": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "10.3.3", "targets": [{"aggregation": "avg", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "keyName": "GRAFANA_TASK_STATUS_TOOK", "legend": "[ Took: Avg within 10s ]", "query": "", "refId": "A", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "max", "bucket": 10000, "command": "ts.range", "datasource": {"uid": "$redis"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_TOOK", "legend": "[ Took: <PERSON> within 10s ]", "query": "", "refId": "B", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}, {"aggregation": "min", "bucket": 10000, "command": "ts.range", "datasource": {"type": "redis-datasource", "uid": "${DS_NEOX-REDIS-MONITOR}"}, "fill": true, "filter": "GRAFANA_TASK_STATUS_NEW", "hide": false, "keyName": "GRAFANA_TASK_STATUS_TOOK", "legend": "[ Took: Min within 10s ]", "query": "", "refId": "C", "section": "stats", "streaming": false, "streamingCapacity": 1000, "streamingDataType": "TimeSeries", "streamingInterval": 1000, "tsGroupByLabel": "", "tsReducer": "avg", "type": "timeSeries", "value": "", "zrangeQuery": "BYSCORE"}], "title": "Status: Took", "transformations": [{"id": "filterFieldsByName", "options": {"byVariable": false, "include": {"variable": "$redis"}}}], "type": "timeseries"}], "title": "Scheduling Status Detail (Fixed Time Window)", "type": "row"}], "refresh": "10s", "schemaVersion": 41, "tags": ["<PERSON><PERSON>"], "templating": {"list": [{"current": {}, "includeAll": false, "label": "Redis", "name": "redis", "options": [], "query": "redis-datasource", "refresh": 1, "regex": "", "type": "datasource"}, {"current": {}, "includeAll": false, "label": "Prometheus", "name": "prometheus", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${prometheus}"}, "definition": "label_values(node_uname_info,job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": {"qryType": 1, "query": "label_values(node_uname_info,job)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(node_uname_info{job=\"$job\"},env)", "includeAll": false, "label": "Env", "name": "env", "options": [], "query": {"qryType": 1, "query": "label_values(node_uname_info{job=\"$job\"},env)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${prometheus}"}, "definition": "label_values(node_uname_info{env=\"$env\"},projectname)", "includeAll": false, "label": "ProjectName", "name": "projectname", "options": [], "query": {"qryType": 1, "query": "label_values(node_uname_info{env=\"$env\"},projectname)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(node_uname_info{projectname=\"$projectname\"},hostname)", "includeAll": true, "label": "HostName", "multi": true, "name": "hostname", "options": [], "query": {"qryType": 1, "query": "label_values(node_uname_info{projectname=\"$projectname\"},hostname)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"auto": false, "auto_count": 30, "auto_min": "10s", "current": {"text": "1m", "value": "1m"}, "description": "CPU Usage -> Time Interval", "label": "TimeInterval", "name": "time_interval", "options": [{"selected": true, "text": "1m", "value": "1m"}, {"selected": false, "text": "3m", "value": "3m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": false, "text": "10m", "value": "10m"}], "query": "1m,3m,5m,10m", "refresh": 2, "type": "interval"}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Kenta - Scheduling Status", "uid": "f6e0410a-2940-4556-a35b-e81ba4e7ece2", "version": 66, "weekStart": ""}