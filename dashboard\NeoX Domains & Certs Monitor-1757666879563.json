{"__inputs": [{"name": "DS_PROMETHEUS", "label": "prometheus", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": {}, "__requires": [{"type": "panel", "id": "gauge", "name": "Gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "12.1.1"}, {"type": "datasource", "id": "loki", "name": "<PERSON>", "version": "12.1.1"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "table", "name": "Table", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Using Prometheus Blackbox Exporter & Domain Exporter", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "decimals": 0, "mappings": [], "noValue": "N/A", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 144, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "domain_expiry_days{domain=~\"$domain\"}", "legendFormat": "{{ domain }}", "range": true, "refId": "A"}], "title": "Domain Expiry Days", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "decimals": 0, "mappings": [], "noValue": "N/A", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 145, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto"}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "ceil((probe_ssl_earliest_cert_expiry{instance=~\"$target\"} - time()) / 86400)", "legendFormat": "{{ instance }}", "range": true, "refId": "A"}], "title": "Certs Expiry Days", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "description": "", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto", "wrapText": false}, "inspect": false}, "decimals": 0, "displayName": "", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "displayName", "value": "Time"}, {"id": "custom.hidden", "value": true}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "Connection"}, {"id": "custom.cellOptions", "value": {"applyToRow": false, "type": "color-background", "wrapText": false}}, {"id": "custom.align", "value": "right"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "dark-red", "value": 0}, {"color": "dark-red", "value": 0}, {"color": "dark-green", "value": 1}]}}, {"id": "mappings", "value": [{"options": {"0": {"index": 1, "text": "Fail"}, "1": {"index": 0, "text": "Success"}}, "type": "value"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Expiration time (days)"}, {"id": "custom.cellOptions", "value": {"type": "color-background"}}, {"id": "custom.align"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)", "value": 0}, {"color": "dark-orange", "value": 7}, {"color": "dark-yellow", "value": 14}, {"color": "dark-green", "value": 30}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instance"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "job"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "domain"}, "properties": [{"id": "displayName", "value": "Domain"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 143, "options": {"cellHeight": "sm", "footer": {"countRows": false, "enablePagination": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Expiration time (days)"}]}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "domain_probe_success{domain=~\"$domain\"} - 0", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "editorMode": "code", "exemplar": true, "expr": "domain_expiry_days{domain=~\"$domain\"} - 0", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{label_name}}", "refId": "B"}], "title": "Domain Status Summary", "transformations": [{"id": "merge", "options": {"reducers": []}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "", "fieldConfig": {"defaults": {"custom": {"align": "auto", "cellOptions": {"type": "auto", "wrapText": false}, "inspect": false}, "decimals": 0, "displayName": "", "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Time"}, "properties": [{"id": "displayName", "value": "Time"}, {"id": "custom.hidden", "value": true}, {"id": "custom.align"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #A"}, "properties": [{"id": "displayName", "value": "Connection"}, {"id": "custom.cellOptions", "value": {"applyToRow": false, "type": "color-background", "wrapText": false}}, {"id": "custom.align", "value": "right"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "dark-red", "value": 0}, {"color": "dark-red", "value": 0}, {"color": "dark-green", "value": 1}]}}, {"id": "mappings", "value": [{"options": {"0": {"index": 1, "text": "Fail"}, "1": {"index": 0, "text": "Success"}}, "type": "value"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "Expiration time (days)"}, {"id": "custom.cellOptions", "value": {"type": "color-background"}}, {"id": "custom.align"}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)", "value": 0}, {"color": "dark-orange", "value": 7}, {"color": "dark-yellow", "value": 14}, {"color": "dark-green", "value": 30}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "env"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "job"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "hostname"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "projectname"}, "properties": [{"id": "custom.hidden", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "instance"}, "properties": [{"id": "displayName", "value": "URI"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 142, "options": {"cellHeight": "sm", "footer": {"countRows": false, "enablePagination": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Expiration time (days)"}]}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "editorMode": "code", "exemplar": true, "expr": "probe_success{instance=~\"$target\"} - 0", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": true, "expr": "ceil((probe_ssl_earliest_cert_expiry{instance=~\"$target\"} - time()) / 86400)", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{label_name}}", "refId": "B"}], "title": "Certs Status Summary", "transformations": [{"id": "merge", "options": {"reducers": []}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 0, "y": 16}, "id": 138, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Last *", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "asc"}}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "probe_duration_seconds{instance=~\"$target\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{ instance }}", "refId": "A"}], "title": "Global Probe Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "axisSoftMax": 1, "axisSoftMin": 0, "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "fieldMinMax": false, "mappings": [{"options": {"0": {"color": "dark-red", "index": 1, "text": "Fail"}, "1": {"color": "dark-green", "index": 0, "text": "Success"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 12, "w": 12, "x": 12, "y": 16}, "id": 141, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Last *", "sortDesc": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "asc"}}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "probe_success{instance=~\"$target\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{ instance }}", "range": true, "refId": "A"}], "title": "Global Probe Status", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 28}, "id": 15, "panels": [], "repeat": "target", "title": "$target status", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}, {"options": {"1": {"text": "UP"}}, "type": "value"}, {"options": {"0": {"text": "DOWN"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": 0}, {"color": "rgba(237, 129, 40, 0.89)", "value": 1}, {"color": "#299c46", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 2, "w": 4, "x": 0, "y": 29}, "id": 2, "maxDataPoints": 100, "maxPerRow": 8, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "probe_success{instance=~\"$target\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "title": "Status", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 10, "x": 4, "y": 29}, "id": 25, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "probe_http_duration_seconds{instance=~\"$target\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{ phase }}", "refId": "B"}], "title": "HTTP Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 10, "x": 14, "y": 29}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "probe_duration_seconds{instance=~\"$target\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "seconds", "refId": "A"}], "title": "Probe Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}, {"options": {"1": {"text": "YES"}}, "type": "value"}, {"options": {"0": {"text": "N/A"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": 0}, {"color": "rgba(237, 129, 40, 0.89)", "value": 201}, {"color": "#d44a3a", "value": 399}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 2, "w": 4, "x": 0, "y": 31}, "id": 20, "maxDataPoints": 100, "maxPerRow": 8, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "probe_http_status_code{instance=~\"$target\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "title": "HTTP Status Code", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 2, "w": 4, "x": 0, "y": 33}, "id": 27, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "probe_http_version{instance=~\"$target\"}", "format": "time_series", "intervalFactor": 1, "refId": "A"}], "title": "HTTP Version", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}, {"options": {"1": {"text": "YES"}}, "type": "value"}, {"options": {"0": {"text": "NO"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": 0}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "#299c46", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 2, "w": 4, "x": 0, "y": 35}, "id": 18, "maxDataPoints": 100, "maxPerRow": 8, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "probe_http_ssl{instance=~\"$target\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "title": "SSL", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"decimals": 2, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}, {"options": {"1": {"text": "YES"}}, "type": "value"}, {"options": {"0": {"text": "NO"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": 0}, {"color": "rgba(237, 129, 40, 0.89)", "value": 604800}, {"color": "#299c46", "value": 1209600}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 2, "w": 10, "x": 4, "y": 35}, "id": 139, "maxDataPoints": 100, "maxPerRow": 8, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "probe_ssl_earliest_cert_expiry{instance=~\"$target\"} - time()", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "range": true, "refId": "A"}], "title": "SSL Expiry", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 2, "w": 5, "x": 14, "y": 35}, "id": 23, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "avg(probe_duration_seconds{instance=~\"$target\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "title": "Average Probe Duration", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 2, "w": 5, "x": 19, "y": 35}, "id": 24, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "expr": "avg(probe_dns_lookup_time_seconds{instance=~\"$target\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "refId": "A"}], "title": "Average DNS Lookup", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 118}, "id": 140, "panels": [], "repeat": "domain", "title": "<PERSON><PERSON> Expiry: $domain", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${prometheus}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 2, "fieldMinMax": false, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}, {"options": {"1": {"text": "YES"}}, "type": "value"}, {"options": {"0": {"text": "NO"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "#d44a3a", "value": 0}, {"color": "rgba(237, 129, 40, 0.89)", "value": 14}, {"color": "#299c46", "value": 30}]}, "unit": "d"}, "overrides": []}, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 119}, "id": 19, "maxDataPoints": 100, "maxPerRow": 8, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.1", "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "domain_expiry_days{domain=~\"$domain\"}", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Domain Expiry", "type": "stat"}], "refresh": "30s", "schemaVersion": 41, "tags": ["blackbox", "prometheus", "domain"], "templating": {"list": [{"allowCustomValue": false, "current": {}, "label": "Prometheus", "name": "prometheus", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "type": "datasource"}, {"auto": true, "auto_count": 10, "auto_min": "10s", "current": {"text": "30s", "value": "30s"}, "label": "Interval", "name": "interval", "options": [{"selected": false, "text": "15s", "value": "15s"}, {"selected": true, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "15s,30s,1m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "refresh": 2, "type": "interval"}, {"allowCustomValue": false, "current": {}, "datasource": {"type": "prometheus", "uid": "${prometheus}"}, "definition": "label_values(probe_success,env)", "includeAll": true, "label": "Env", "multi": true, "name": "env", "options": [], "query": {"qryType": 1, "query": "label_values(probe_success,env)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"allowCustomValue": false, "current": {}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(probe_success{env=\"$env\"},instance)", "includeAll": true, "label": "Target", "multi": true, "name": "target", "options": [], "query": {"qryType": 1, "query": "label_values(probe_success{env=\"$env\"},instance)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 1, "regex": "", "type": "query"}, {"allowCustomValue": false, "current": {"text": ["$__all"], "value": ["$__all"]}, "includeAll": true, "label": "Domain", "multi": true, "name": "domain", "options": [{"selected": false, "text": "yakumaru.ai", "value": "yakumaru.ai"}, {"selected": false, "text": "mapsseries.com", "value": "mapsseries.com"}, {"selected": false, "text": "neox-inc.com", "value": "neox-inc.com"}, {"selected": false, "text": "moair.net", "value": "moair.net"}, {"selected": false, "text": "luckyisle.net", "value": "luckyisle.net"}, {"selected": false, "text": "grew-jp.net", "value": "grew-jp.net"}], "query": "yakumaru.ai,mapsseries.com,neox-inc.com,moair.net,luckyisle.net,grew-jp.net", "type": "custom"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "NeoX Domains & Certs Monitor", "uid": "xtkCtBkiz", "version": 17, "weekStart": ""}